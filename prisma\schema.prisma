// HashCoreX Database Schema
// Solar-powered cloud mining investment platform

generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management
model User {
  id                String    @id @default(cuid())
  email             String    @unique
  passwordHash      String
  referralId        String    @unique @default(cuid())
  leftReferralId    String?
  rightReferralId   String?
  kycStatus         KYCStatus @default(PENDING)
  isActive          Boolean   @default(true)
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt

  // Relations
  miningUnits       MiningUnit[]
  transactions      Transaction[]
  kycDocuments      KYCDocument[]
  withdrawalRequests WithdrawalRequest[]
  binaryPoints      BinaryPoints[]

  // Referral relations
  directReferrals   Referral[] @relation("ReferrerRelation")
  referredBy        Referral[] @relation("ReferredRelation")

  @@map("users")
}

// KYC Management
model KYCDocument {
  id           String    @id @default(cuid())
  userId       String
  documentType DocumentType
  filePath     String
  status       KYCStatus @default(PENDING)
  reviewedAt   DateTime?
  reviewedBy   String?
  rejectionReason String?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc_documents")
}

// Mining Units
model MiningUnit {
  id               String           @id @default(cuid())
  userId           String
  thsAmount        Float
  investmentAmount Float
  startDate        DateTime         @default(now())
  expiryDate       DateTime
  dailyROI         Float
  totalEarned      Float            @default(0)
  status           MiningUnitStatus @default(ACTIVE)
  createdAt        DateTime         @default(now())
  updatedAt        DateTime         @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("mining_units")
}

// Transaction Management
model Transaction {
  id          String            @id @default(cuid())
  userId      String
  type        TransactionType
  amount      Float
  description String
  status      TransactionStatus @default(PENDING)
  reference   String?           // For external references like withdrawal TXIDs
  createdAt   DateTime          @default(now())
  updatedAt   DateTime          @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

// Referral System
model Referral {
  id              String        @id @default(cuid())
  referrerId      String
  referredId      String
  placementSide   PlacementSide
  commissionEarned Float        @default(0)
  isActive        Boolean       @default(true)
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  referrer User @relation("ReferrerRelation", fields: [referrerId], references: [id], onDelete: Cascade)
  referred User @relation("ReferredRelation", fields: [referredId], references: [id], onDelete: Cascade)

  @@unique([referrerId, referredId])
  @@map("referrals")
}

// Binary Points System
model BinaryPoints {
  id            String    @id @default(cuid())
  userId        String
  leftPoints    Float     @default(0)
  rightPoints   Float     @default(0)
  matchedPoints Float     @default(0)
  flushDate     DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("binary_points")
}

// Withdrawal Management
model WithdrawalRequest {
  id          String           @id @default(cuid())
  userId      String
  amount      Float
  usdtAddress String
  status      WithdrawalStatus @default(PENDING)
  processedAt DateTime?
  processedBy String?
  txid        String?          // Transaction ID after processing
  rejectionReason String?
  createdAt   DateTime         @default(now())
  updatedAt   DateTime         @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("withdrawal_requests")
}

// Admin Settings
model AdminSettings {
  key       String   @id
  value     String
  updatedAt DateTime @updatedAt
  updatedBy String?

  @@map("admin_settings")
}

// System Logs for Audit Trail
model SystemLog {
  id        String   @id @default(cuid())
  action    String
  userId    String?
  adminId   String?
  details   Json?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  @@map("system_logs")
}

// Binary Pool Management
model BinaryPool {
  id              String   @id @default(cuid())
  totalPool       Float    @default(0)
  distributedPool Float    @default(0)
  remainingPool   Float    @default(0)
  distributionDate DateTime @default(now())
  createdAt       DateTime @default(now())

  @@map("binary_pool")
}

// Enums
enum KYCStatus {
  PENDING
  APPROVED
  REJECTED
}

enum DocumentType {
  ID
  SELFIE
}

enum MiningUnitStatus {
  ACTIVE
  EXPIRED
}

enum TransactionType {
  MINING_EARNINGS
  DIRECT_REFERRAL
  BINARY_BONUS
  DEPOSIT
  WITHDRAWAL
  PURCHASE
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
  CANCELLED
}

enum PlacementSide {
  LEFT
  RIGHT
}

enum WithdrawalStatus {
  PENDING
  APPROVED
  REJECTED
  COMPLETED
}
