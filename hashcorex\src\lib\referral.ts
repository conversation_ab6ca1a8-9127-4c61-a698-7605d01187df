import { prisma } from './prisma';
import { referralDb, binaryPointsDb, transactionDb, adminSettingsDb, systemLogDb } from './database';

// Place new user in binary tree (left or right side)
export async function placeUserInBinaryTree(referrerId: string, newUserId: string): Promise<'LEFT' | 'RIGHT'> {
  try {
    // Get referrer's current referrals
    const existingReferrals = await referralDb.findByReferrerId(referrerId);
    
    // Count left and right placements
    const leftCount = existingReferrals.filter(r => r.placementSide === 'LEFT').length;
    const rightCount = existingReferrals.filter(r => r.placementSide === 'RIGHT').length;
    
    // Place in weaker leg (or left if equal)
    const placementSide: 'LEFT' | 'RIGHT' = leftCount <= rightCount ? 'LEFT' : 'RIGHT';
    
    // Create referral relationship
    await referralDb.create({
      referrerId,
      referredId: newUserId,
      placementSide,
    });
    
    // Update referrer's left/right referral IDs
    const updateData = placementSide === 'LEFT' 
      ? { leftReferralId: newUserId }
      : { rightReferralId: newUserId };
    
    await prisma.user.update({
      where: { id: referrerId },
      data: updateData,
    });
    
    return placementSide;
    
  } catch (error) {
    console.error('Binary tree placement error:', error);
    throw error;
  }
}

// Place user in specific side (left or right)
export async function placeUserInSpecificSide(referrerId: string, newUserId: string, side: 'LEFT' | 'RIGHT'): Promise<'LEFT' | 'RIGHT'> {
  try {
    // Check if the specific side is already occupied
    const existingReferrals = await referralDb.findByReferrerId(referrerId);
    const sideOccupied = existingReferrals.some(r => r.placementSide === side);

    if (sideOccupied) {
      // If specific side is occupied, find the next available spot in that leg
      const nextAvailableSpot = await findNextAvailableSpotInLeg(referrerId, side);
      if (nextAvailableSpot) {
        await referralDb.create({
          referrerId: nextAvailableSpot.userId,
          referredId: newUserId,
          placementSide: nextAvailableSpot.side,
        });

        // Update the parent's referral ID
        const updateData = nextAvailableSpot.side === 'LEFT'
          ? { leftReferralId: newUserId }
          : { rightReferralId: newUserId };

        await prisma.user.update({
          where: { id: nextAvailableSpot.userId },
          data: updateData,
        });

        return nextAvailableSpot.side;
      } else {
        // Fallback to automatic placement if no spot found
        return await placeUserInBinaryTree(referrerId, newUserId);
      }
    } else {
      // Place directly in the requested side
      await referralDb.create({
        referrerId,
        referredId: newUserId,
        placementSide: side,
      });

      // Update referrer's left/right referral IDs
      const updateData = side === 'LEFT'
        ? { leftReferralId: newUserId }
        : { rightReferralId: newUserId };

      await prisma.user.update({
        where: { id: referrerId },
        data: updateData,
      });

      return side;
    }

  } catch (error) {
    console.error('Specific side placement error:', error);
    throw error;
  }
}

// Find next available spot in a specific leg
async function findNextAvailableSpotInLeg(rootUserId: string, targetSide: 'LEFT' | 'RIGHT'): Promise<{ userId: string; side: 'LEFT' | 'RIGHT' } | null> {
  try {
    // Get the first user in the target leg
    const rootReferrals = await referralDb.findByReferrerId(rootUserId);
    const firstInLeg = rootReferrals.find(r => r.placementSide === targetSide);

    if (!firstInLeg) {
      // The target side is completely empty
      return { userId: rootUserId, side: targetSide };
    }

    // Traverse down the leg to find the first available spot
    const queue = [firstInLeg.referredId];

    while (queue.length > 0) {
      const currentUserId = queue.shift()!;
      const currentReferrals = await referralDb.findByReferrerId(currentUserId);

      // Check if this user has any empty spots
      const hasLeft = currentReferrals.some(r => r.placementSide === 'LEFT');
      const hasRight = currentReferrals.some(r => r.placementSide === 'RIGHT');

      if (!hasLeft) {
        return { userId: currentUserId, side: 'LEFT' };
      }
      if (!hasRight) {
        return { userId: currentUserId, side: 'RIGHT' };
      }

      // Add children to queue for further traversal
      currentReferrals.forEach(r => {
        queue.push(r.referredId);
      });
    }

    return null; // No available spot found
  } catch (error) {
    console.error('Find available spot error:', error);
    return null;
  }
}

// Process direct referral bonus (10% of investment)
export async function processDirectReferralBonus(referrerId: string, investmentAmount: number) {
  try {
    const bonusPercentage = parseFloat(await adminSettingsDb.get('DIRECT_REFERRAL_BONUS') || '10');
    const bonusAmount = (investmentAmount * bonusPercentage) / 100;
    
    // Create direct referral transaction
    await transactionDb.create({
      userId: referrerId,
      type: 'DIRECT_REFERRAL',
      amount: bonusAmount,
      description: `Direct referral bonus (${bonusPercentage}% of $${investmentAmount})`,
      status: 'COMPLETED',
    });
    
    // Update referral commission earned
    await prisma.referral.updateMany({
      where: {
        referrerId,
        referred: {
          miningUnits: {
            some: {
              investmentAmount,
            },
          },
        },
      },
      data: {
        commissionEarned: {
          increment: bonusAmount,
        },
      },
    });
    
    return bonusAmount;
    
  } catch (error) {
    console.error('Direct referral bonus error:', error);
    throw error;
  }
}

// Add points to binary system when someone makes an investment
export async function addBinaryPoints(userId: string, investmentAmount: number) {
  try {
    // Find all upline users and add points to their binary system
    const uplineUsers = await getUplineUsers(userId);
    
    for (const uplineUser of uplineUsers) {
      // Determine which side this user is on relative to upline
      const placementSide = await getUserPlacementSide(uplineUser.id, userId);
      
      if (placementSide) {
        // Add points to the appropriate side
        const pointsToAdd = placementSide === 'LEFT' 
          ? { leftPoints: investmentAmount }
          : { rightPoints: investmentAmount };
        
        await binaryPointsDb.upsert({
          userId: uplineUser.id,
          ...pointsToAdd,
        });
      }
    }
    
  } catch (error) {
    console.error('Binary points addition error:', error);
    throw error;
  }
}

// Get all upline users for a given user
async function getUplineUsers(userId: string): Promise<Array<{ id: string; email: string }>> {
  try {
    const uplineUsers = [];
    let currentUserId = userId;
    
    // Traverse up the tree (maximum 10 levels to prevent infinite loops)
    for (let level = 0; level < 10; level++) {
      const referral = await prisma.referral.findFirst({
        where: { referredId: currentUserId },
        include: {
          referrer: {
            select: { id: true, email: true },
          },
        },
      });
      
      if (!referral) break;
      
      uplineUsers.push(referral.referrer);
      currentUserId = referral.referrerId;
    }
    
    return uplineUsers;
    
  } catch (error) {
    console.error('Upline users fetch error:', error);
    return [];
  }
}

// Determine which side a user is on relative to an upline user
async function getUserPlacementSide(uplineUserId: string, userId: string): Promise<'LEFT' | 'RIGHT' | null> {
  try {
    // Check direct placement first
    const directReferral = await prisma.referral.findFirst({
      where: {
        referrerId: uplineUserId,
        referredId: userId,
      },
    });
    
    if (directReferral) {
      return directReferral.placementSide;
    }
    
    // Check indirect placement by traversing down the tree
    const leftSideUsers = await getDownlineUsers(uplineUserId, 'LEFT');
    const rightSideUsers = await getDownlineUsers(uplineUserId, 'RIGHT');
    
    if (leftSideUsers.some(u => u.id === userId)) {
      return 'LEFT';
    }
    
    if (rightSideUsers.some(u => u.id === userId)) {
      return 'RIGHT';
    }
    
    return null;
    
  } catch (error) {
    console.error('Placement side determination error:', error);
    return null;
  }
}

// Get all downline users for a specific side
async function getDownlineUsers(userId: string, side: 'LEFT' | 'RIGHT'): Promise<Array<{ id: string }>> {
  try {
    const downlineUsers = [];
    const queue = [userId];
    
    while (queue.length > 0) {
      const currentUserId = queue.shift()!;
      
      const referrals = await prisma.referral.findMany({
        where: {
          referrerId: currentUserId,
          placementSide: side,
        },
        select: {
          referredId: true,
        },
      });
      
      for (const referral of referrals) {
        downlineUsers.push({ id: referral.referredId });
        queue.push(referral.referredId);
      }
    }
    
    return downlineUsers;
    
  } catch (error) {
    console.error('Downline users fetch error:', error);
    return [];
  }
}

// Process daily binary matching (12:00 AM UTC)
export async function processBinaryMatching() {
  try {
    console.log('Starting binary matching process...');
    
    const maxPointsPerSide = parseFloat(await adminSettingsDb.get('MAX_BINARY_POINTS_PER_SIDE') || '2000');
    const binaryPoolPercentage = parseFloat(await adminSettingsDb.get('BINARY_POOL_PERCENTAGE') || '30');
    
    // Get current binary pool amount
    const totalInvestments = await prisma.miningUnit.aggregate({
      _sum: { investmentAmount: true },
      where: {
        createdAt: {
          gte: new Date(Date.now() - 24 * 60 * 60 * 1000), // Last 24 hours
        },
      },
    });
    
    const dailyBinaryPool = ((totalInvestments._sum.investmentAmount || 0) * binaryPoolPercentage) / 100;
    
    // Get all users with binary points
    const usersWithPoints = await prisma.binaryPoints.findMany({
      where: {
        OR: [
          { leftPoints: { gt: 0 } },
          { rightPoints: { gt: 0 } },
        ],
      },
      include: {
        user: {
          select: { id: true, email: true },
        },
      },
    });
    
    console.log(`Processing binary matching for ${usersWithPoints.length} users`);
    
    let totalMatchedPoints = 0;
    const matchingResults = [];
    
    for (const userPoints of usersWithPoints) {
      try {
        // Calculate matching points (minimum of left and right, capped at max per side)
        const leftPoints = Math.min(userPoints.leftPoints, maxPointsPerSide);
        const rightPoints = Math.min(userPoints.rightPoints, maxPointsPerSide);
        const matchedPoints = Math.min(leftPoints, rightPoints);
        
        if (matchedPoints > 0) {
          // Calculate payout from binary pool
          const userPayout = (matchedPoints / Math.max(totalMatchedPoints, 1)) * dailyBinaryPool;
          
          if (userPayout > 0) {
            // Create binary bonus transaction
            await transactionDb.create({
              userId: userPoints.userId,
              type: 'BINARY_BONUS',
              amount: userPayout,
              description: `Binary matching bonus - ${matchedPoints} points matched`,
              status: 'COMPLETED',
            });
          }
          
          // Update matched points and reset used points
          await prisma.binaryPoints.update({
            where: { id: userPoints.id },
            data: {
              leftPoints: Math.max(0, userPoints.leftPoints - matchedPoints),
              rightPoints: Math.max(0, userPoints.rightPoints - matchedPoints),
              matchedPoints: { increment: matchedPoints },
              flushDate: new Date(),
            },
          });
          
          matchingResults.push({
            userId: userPoints.userId,
            matchedPoints,
            payout: userPayout,
          });
          
          totalMatchedPoints += matchedPoints;
        }
        
      } catch (userError) {
        console.error(`Error processing binary matching for user ${userPoints.userId}:`, userError);
      }
    }
    
    // Log the binary matching process
    await systemLogDb.create({
      action: 'BINARY_MATCHING_PROCESSED',
      details: {
        usersProcessed: usersWithPoints.length,
        totalMatchedPoints,
        dailyBinaryPool,
        totalPayouts: matchingResults.reduce((sum, r) => sum + r.payout, 0),
        timestamp: new Date().toISOString(),
      },
    });
    
    console.log(`Binary matching completed. Processed ${matchingResults.length} users with ${totalMatchedPoints} total matched points.`);
    return matchingResults;
    
  } catch (error) {
    console.error('Binary matching process error:', error);
    throw error;
  }
}

// Get binary tree structure for a user
export async function getBinaryTreeStructure(userId: string, depth = 3) {
  try {
    const buildTree = async (currentUserId: string, currentDepth: number): Promise<any> => {
      if (currentDepth <= 0) return null;
      
      const user = await prisma.user.findUnique({
        where: { id: currentUserId },
        select: {
          id: true,
          email: true,
          createdAt: true,
        },
      });
      
      if (!user) return null;
      
      // Get direct referrals
      const leftReferral = await prisma.referral.findFirst({
        where: {
          referrerId: currentUserId,
          placementSide: 'LEFT',
        },
        include: {
          referred: {
            select: { id: true, email: true, createdAt: true },
          },
        },
      });
      
      const rightReferral = await prisma.referral.findFirst({
        where: {
          referrerId: currentUserId,
          placementSide: 'RIGHT',
        },
        include: {
          referred: {
            select: { id: true, email: true, createdAt: true },
          },
        },
      });
      
      // Get binary points
      const binaryPoints = await binaryPointsDb.findByUserId(currentUserId);
      
      return {
        user,
        binaryPoints: binaryPoints || { leftPoints: 0, rightPoints: 0, matchedPoints: 0 },
        leftChild: leftReferral ? await buildTree(leftReferral.referredId, currentDepth - 1) : null,
        rightChild: rightReferral ? await buildTree(rightReferral.referredId, currentDepth - 1) : null,
      };
    };
    
    return await buildTree(userId, depth);
    
  } catch (error) {
    console.error('Binary tree structure error:', error);
    throw error;
  }
}
