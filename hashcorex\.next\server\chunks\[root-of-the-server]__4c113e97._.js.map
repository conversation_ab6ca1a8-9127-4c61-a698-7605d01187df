{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client';\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\nexport const prisma = globalForPrisma.prisma ?? new PrismaClient();\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma;\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SAAS,gBAAgB,MAAM,IAAI,IAAI,6HAAA,CAAA,eAAY;AAEhE,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 114, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/database.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { User, MiningUnit, Transaction, Referral, BinaryPoints } from '@/types';\n\n// User Database Operations\nexport const userDb = {\n  async create(data: {\n    email: string;\n    firstName: string;\n    lastName: string;\n    password: string;\n    referralId?: string;\n  }) {\n    return await prisma.user.create({\n      data: {\n        email: data.email,\n        firstName: data.firstName,\n        lastName: data.lastName,\n        password: data.password,\n        referralId: data.referralId || undefined,\n      },\n    });\n  },\n\n  async findByEmail(email: string) {\n    return await prisma.user.findUnique({\n      where: { email },\n      include: {\n        miningUnits: true,\n        transactions: true,\n        binaryPoints: true,\n      },\n    });\n  },\n\n  async findById(id: string) {\n    return await prisma.user.findUnique({\n      where: { id },\n      include: {\n        miningUnits: true,\n        transactions: true,\n        binaryPoints: true,\n      },\n    });\n  },\n\n  async findByReferralId(referralId: string) {\n    return await prisma.user.findUnique({\n      where: { referralId },\n    });\n  },\n\n  async update(id: string, data: Partial<{\n    firstName: string;\n    lastName: string;\n    email: string;\n    role: 'USER' | 'ADMIN';\n    isActive: boolean;\n    kycStatus: 'PENDING' | 'APPROVED' | 'REJECTED';\n  }>) {\n    return await prisma.user.update({\n      where: { id },\n      data,\n    });\n  },\n\n  async updateKYCStatus(userId: string, status: 'PENDING' | 'APPROVED' | 'REJECTED') {\n    return await prisma.user.update({\n      where: { id: userId },\n      data: { kycStatus: status },\n    });\n  },\n};\n\n// Mining Unit Database Operations\nexport const miningUnitDb = {\n  async create(data: {\n    userId: string;\n    thsAmount: number;\n    investmentAmount: number;\n    dailyROI: number;\n  }) {\n    const expiryDate = new Date();\n    expiryDate.setFullYear(expiryDate.getFullYear() + 1); // 12 months from now\n\n    return await prisma.miningUnit.create({\n      data: {\n        userId: data.userId,\n        thsAmount: data.thsAmount,\n        investmentAmount: data.investmentAmount,\n        dailyROI: data.dailyROI,\n        expiryDate,\n      },\n    });\n  },\n\n  async findActiveByUserId(userId: string) {\n    return await prisma.miningUnit.findMany({\n      where: {\n        userId,\n        status: 'ACTIVE',\n        expiryDate: {\n          gt: new Date(),\n        },\n      },\n    });\n  },\n\n  async updateTotalEarned(unitId: string, amount: number) {\n    return await prisma.miningUnit.update({\n      where: { id: unitId },\n      data: {\n        totalEarned: {\n          increment: amount,\n        },\n      },\n    });\n  },\n\n  async expireUnit(unitId: string) {\n    return await prisma.miningUnit.update({\n      where: { id: unitId },\n      data: { status: 'EXPIRED' },\n    });\n  },\n};\n\n// Transaction Database Operations\nexport const transactionDb = {\n  async create(data: {\n    userId: string;\n    type: 'MINING_EARNINGS' | 'DIRECT_REFERRAL' | 'BINARY_BONUS' | 'DEPOSIT' | 'WITHDRAWAL' | 'PURCHASE';\n    amount: number;\n    description: string;\n    status?: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED';\n  }) {\n    return await prisma.transaction.create({\n      data: {\n        userId: data.userId,\n        type: data.type,\n        amount: data.amount,\n        description: data.description,\n        status: data.status || 'PENDING',\n      },\n    });\n  },\n\n  async findByUserId(userId: string, limit = 50) {\n    return await prisma.transaction.findMany({\n      where: { userId },\n      orderBy: { createdAt: 'desc' },\n      take: limit,\n    });\n  },\n\n  async updateStatus(transactionId: string, status: 'PENDING' | 'COMPLETED' | 'FAILED' | 'CANCELLED') {\n    return await prisma.transaction.update({\n      where: { id: transactionId },\n      data: { status },\n    });\n  },\n};\n\n// Referral Database Operations\nexport const referralDb = {\n  async create(data: {\n    referrerId: string;\n    referredId: string;\n    placementSide: 'LEFT' | 'RIGHT';\n  }) {\n    return await prisma.referral.create({\n      data: {\n        referrerId: data.referrerId,\n        referredId: data.referredId,\n        placementSide: data.placementSide,\n      },\n    });\n  },\n\n  async findByReferrerId(referrerId: string) {\n    return await prisma.referral.findMany({\n      where: { referrerId },\n      include: {\n        referred: {\n          select: {\n            id: true,\n            email: true,\n            createdAt: true,\n          },\n        },\n      },\n    });\n  },\n};\n\n// Binary Points Database Operations\nexport const binaryPointsDb = {\n  async upsert(data: {\n    userId: string;\n    leftPoints?: number;\n    rightPoints?: number;\n  }) {\n    return await prisma.binaryPoints.upsert({\n      where: { userId: data.userId },\n      update: {\n        leftPoints: data.leftPoints !== undefined ? { increment: data.leftPoints } : undefined,\n        rightPoints: data.rightPoints !== undefined ? { increment: data.rightPoints } : undefined,\n      },\n      create: {\n        userId: data.userId,\n        leftPoints: data.leftPoints || 0,\n        rightPoints: data.rightPoints || 0,\n      },\n    });\n  },\n\n  async findByUserId(userId: string) {\n    return await prisma.binaryPoints.findUnique({\n      where: { userId },\n    });\n  },\n\n  async resetPoints(userId: string, leftPoints: number, rightPoints: number) {\n    return await prisma.binaryPoints.update({\n      where: { userId },\n      data: {\n        leftPoints,\n        rightPoints,\n        flushDate: new Date(),\n      },\n    });\n  },\n};\n\n// Withdrawal Database Operations\nexport const withdrawalDb = {\n  async create(data: {\n    userId: string;\n    amount: number;\n    usdtAddress: string;\n  }) {\n    return await prisma.withdrawalRequest.create({\n      data: {\n        userId: data.userId,\n        amount: data.amount,\n        usdtAddress: data.usdtAddress,\n      },\n    });\n  },\n\n  async findPending() {\n    return await prisma.withdrawalRequest.findMany({\n      where: { status: 'PENDING' },\n      include: {\n        user: {\n          select: {\n            id: true,\n            email: true,\n            kycStatus: true,\n          },\n        },\n      },\n      orderBy: { createdAt: 'asc' },\n    });\n  },\n\n  async updateStatus(\n    requestId: string, \n    status: 'APPROVED' | 'REJECTED' | 'COMPLETED',\n    processedBy?: string,\n    txid?: string,\n    rejectionReason?: string\n  ) {\n    return await prisma.withdrawalRequest.update({\n      where: { id: requestId },\n      data: {\n        status,\n        processedBy,\n        txid,\n        rejectionReason,\n        processedAt: new Date(),\n      },\n    });\n  },\n};\n\n// Admin Settings Database Operations\nexport const adminSettingsDb = {\n  async get(key: string) {\n    const setting = await prisma.adminSettings.findUnique({\n      where: { key },\n    });\n    return setting?.value;\n  },\n\n  async set(key: string, value: string, updatedBy?: string) {\n    return await prisma.adminSettings.upsert({\n      where: { key },\n      update: { value, updatedBy },\n      create: { key, value, updatedBy },\n    });\n  },\n\n  async getAll() {\n    return await prisma.adminSettings.findMany();\n  },\n};\n\n// System Logs\nexport const systemLogDb = {\n  async create(data: {\n    action: string;\n    userId?: string;\n    adminId?: string;\n    details?: any;\n    ipAddress?: string;\n    userAgent?: string;\n  }) {\n    return await prisma.systemLog.create({\n      data: {\n        action: data.action,\n        userId: data.userId,\n        adminId: data.adminId,\n        details: data.details ? JSON.stringify(data.details) : null,\n        ipAddress: data.ipAddress,\n        userAgent: data.userAgent,\n      },\n    });\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;AAIO,MAAM,SAAS;IACpB,MAAM,QAAO,IAMZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,MAAM;gBACJ,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,UAAU,KAAK,QAAQ;gBACvB,YAAY,KAAK,UAAU,IAAI;YACjC;QACF;IACF;IAEA,MAAM,aAAY,KAAa;QAC7B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAM;YACf,SAAS;gBACP,aAAa;gBACb,cAAc;gBACd,cAAc;YAChB;QACF;IACF;IAEA,MAAM,UAAS,EAAU;QACvB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAG;YACZ,SAAS;gBACP,aAAa;gBACb,cAAc;gBACd,cAAc;YAChB;QACF;IACF;IAEA,MAAM,kBAAiB,UAAkB;QACvC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,UAAU,CAAC;YAClC,OAAO;gBAAE;YAAW;QACtB;IACF;IAEA,MAAM,QAAO,EAAU,EAAE,IAOvB;QACA,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE;YAAG;YACZ;QACF;IACF;IAEA,MAAM,iBAAgB,MAAc,EAAE,MAA2C;QAC/E,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YAC9B,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,WAAW;YAAO;QAC5B;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,QAAO,IAKZ;QACC,MAAM,aAAa,IAAI;QACvB,WAAW,WAAW,CAAC,WAAW,WAAW,KAAK,IAAI,qBAAqB;QAE3E,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,WAAW,KAAK,SAAS;gBACzB,kBAAkB,KAAK,gBAAgB;gBACvC,UAAU,KAAK,QAAQ;gBACvB;YACF;QACF;IACF;IAEA,MAAM,oBAAmB,MAAc;QACrC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACtC,OAAO;gBACL;gBACA,QAAQ;gBACR,YAAY;oBACV,IAAI,IAAI;gBACV;YACF;QACF;IACF;IAEA,MAAM,mBAAkB,MAAc,EAAE,MAAc;QACpD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBACJ,aAAa;oBACX,WAAW;gBACb;YACF;QACF;IACF;IAEA,MAAM,YAAW,MAAc;QAC7B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,MAAM,CAAC;YACpC,OAAO;gBAAE,IAAI;YAAO;YACpB,MAAM;gBAAE,QAAQ;YAAU;QAC5B;IACF;AACF;AAGO,MAAM,gBAAgB;IAC3B,MAAM,QAAO,IAMZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YACrC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,MAAM,KAAK,IAAI;gBACf,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;gBAC7B,QAAQ,KAAK,MAAM,IAAI;YACzB;QACF;IACF;IAEA,MAAM,cAAa,MAAc,EAAE,QAAQ,EAAE;QAC3C,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACvC,OAAO;gBAAE;YAAO;YAChB,SAAS;gBAAE,WAAW;YAAO;YAC7B,MAAM;QACR;IACF;IAEA,MAAM,cAAa,aAAqB,EAAE,MAAwD;QAChG,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,MAAM,CAAC;YACrC,OAAO;gBAAE,IAAI;YAAc;YAC3B,MAAM;gBAAE;YAAO;QACjB;IACF;AACF;AAGO,MAAM,aAAa;IACxB,MAAM,QAAO,IAIZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAClC,MAAM;gBACJ,YAAY,KAAK,UAAU;gBAC3B,YAAY,KAAK,UAAU;gBAC3B,eAAe,KAAK,aAAa;YACnC;QACF;IACF;IAEA,MAAM,kBAAiB,UAAkB;QACvC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;YACpC,OAAO;gBAAE;YAAW;YACpB,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;oBACb;gBACF;YACF;QACF;IACF;AACF;AAGO,MAAM,iBAAiB;IAC5B,MAAM,QAAO,IAIZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE,QAAQ,KAAK,MAAM;YAAC;YAC7B,QAAQ;gBACN,YAAY,KAAK,UAAU,KAAK,YAAY;oBAAE,WAAW,KAAK,UAAU;gBAAC,IAAI;gBAC7E,aAAa,KAAK,WAAW,KAAK,YAAY;oBAAE,WAAW,KAAK,WAAW;gBAAC,IAAI;YAClF;YACA,QAAQ;gBACN,QAAQ,KAAK,MAAM;gBACnB,YAAY,KAAK,UAAU,IAAI;gBAC/B,aAAa,KAAK,WAAW,IAAI;YACnC;QACF;IACF;IAEA,MAAM,cAAa,MAAc;QAC/B,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE;YAAO;QAClB;IACF;IAEA,MAAM,aAAY,MAAc,EAAE,UAAkB,EAAE,WAAmB;QACvE,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,MAAM,CAAC;YACtC,OAAO;gBAAE;YAAO;YAChB,MAAM;gBACJ;gBACA;gBACA,WAAW,IAAI;YACjB;QACF;IACF;AACF;AAGO,MAAM,eAAe;IAC1B,MAAM,QAAO,IAIZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC3C,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;gBACnB,aAAa,KAAK,WAAW;YAC/B;QACF;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,QAAQ,CAAC;YAC7C,OAAO;gBAAE,QAAQ;YAAU;YAC3B,SAAS;gBACP,MAAM;oBACJ,QAAQ;wBACN,IAAI;wBACJ,OAAO;wBACP,WAAW;oBACb;gBACF;YACF;YACA,SAAS;gBAAE,WAAW;YAAM;QAC9B;IACF;IAEA,MAAM,cACJ,SAAiB,EACjB,MAA6C,EAC7C,WAAoB,EACpB,IAAa,EACb,eAAwB;QAExB,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,iBAAiB,CAAC,MAAM,CAAC;YAC3C,OAAO;gBAAE,IAAI;YAAU;YACvB,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA,aAAa,IAAI;YACnB;QACF;IACF;AACF;AAGO,MAAM,kBAAkB;IAC7B,MAAM,KAAI,GAAW;QACnB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,UAAU,CAAC;YACpD,OAAO;gBAAE;YAAI;QACf;QACA,OAAO,SAAS;IAClB;IAEA,MAAM,KAAI,GAAW,EAAE,KAAa,EAAE,SAAkB;QACtD,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,MAAM,CAAC;YACvC,OAAO;gBAAE;YAAI;YACb,QAAQ;gBAAE;gBAAO;YAAU;YAC3B,QAAQ;gBAAE;gBAAK;gBAAO;YAAU;QAClC;IACF;IAEA,MAAM;QACJ,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,aAAa,CAAC,QAAQ;IAC5C;AACF;AAGO,MAAM,cAAc;IACzB,MAAM,QAAO,IAOZ;QACC,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,MAAM,CAAC;YACnC,MAAM;gBACJ,QAAQ,KAAK,MAAM;gBACnB,QAAQ,KAAK,MAAM;gBACnB,SAAS,KAAK,OAAO;gBACrB,SAAS,KAAK,OAAO,GAAG,KAAK,SAAS,CAAC,KAAK,OAAO,IAAI;gBACvD,WAAW,KAAK,SAAS;gBACzB,WAAW,KAAK,SAAS;YAC3B;QACF;IACF;AACF", "debugId": null}}, {"offset": {"line": 431, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/auth.ts"], "sourcesContent": ["import bcrypt from 'bcryptjs';\nimport jwt from 'jsonwebtoken';\nimport { NextRequest } from 'next/server';\nimport { userDb } from './database';\n\nconst JWT_SECRET = process.env.JWT_SECRET || 'fallback-secret-key';\nconst JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || '7d';\n\n// Password utilities\nexport const hashPassword = async (password: string): Promise<string> => {\n  return await bcrypt.hash(password, 12);\n};\n\nexport const verifyPassword = async (password: string, hashedPassword: string): Promise<boolean> => {\n  return await bcrypt.compare(password, hashedPassword);\n};\n\n// JWT utilities\nexport const generateToken = (payload: { userId: string; email: string }): string => {\n  return jwt.sign(payload, JWT_SECRET, { expiresIn: JWT_EXPIRES_IN });\n};\n\nexport const verifyToken = (token: string): { userId: string; email: string } | null => {\n  try {\n    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string; email: string };\n    return decoded;\n  } catch (error) {\n    return null;\n  }\n};\n\n// Generate unique referral ID\nexport const generateReferralId = (): string => {\n  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';\n  let result = 'HC'; // HashCoreX prefix\n  for (let i = 0; i < 8; i++) {\n    result += chars.charAt(Math.floor(Math.random() * chars.length));\n  }\n  return result;\n};\n\n// Authentication middleware\nexport const authenticateRequest = async (request: NextRequest) => {\n  const token = request.headers.get('authorization')?.replace('Bearer ', '') ||\n                request.cookies.get('auth-token')?.value;\n\n  if (!token) {\n    return { authenticated: false, user: null };\n  }\n\n  const decoded = verifyToken(token);\n  if (!decoded) {\n    return { authenticated: false, user: null };\n  }\n\n  const user = await userDb.findByEmail(decoded.email);\n  if (!user || !user.isActive) {\n    return { authenticated: false, user: null };\n  }\n\n  return { authenticated: true, user };\n};\n\n// User registration\nexport const registerUser = async (data: {\n  email: string;\n  firstName: string;\n  lastName: string;\n  password: string;\n  referralCode?: string;\n  placementSide?: 'left' | 'right';\n}) => {\n  // Check if user already exists\n  const existingUser = await userDb.findByEmail(data.email);\n  if (existingUser) {\n    throw new Error('User already exists with this email');\n  }\n\n  // Validate referral code if provided\n  let referrerId: string | undefined;\n  if (data.referralCode) {\n    const referrer = await userDb.findByReferralId(data.referralCode);\n    if (!referrer) {\n      throw new Error('Invalid referral code');\n    }\n    referrerId = referrer.id;\n  }\n\n  // Hash password\n  const passwordHash = await hashPassword(data.password);\n\n  // Generate unique referral ID\n  let referralId: string;\n  let isUnique = false;\n  do {\n    referralId = generateReferralId();\n    const existing = await userDb.findByReferralId(referralId);\n    isUnique = !existing;\n  } while (!isUnique);\n\n\n\n  // Create user in PostgreSQL\n  const user = await userDb.create({\n    email: data.email,\n    firstName: data.firstName,\n    lastName: data.lastName,\n    password: passwordHash,\n    referralId,\n  });\n\n  // Create referral relationship if referrer exists\n  if (referrerId) {\n    const { placeUserInBinaryTree, placeUserInSpecificSide } = await import('./referral');\n\n    if (data.placementSide) {\n      // Place user in specific side if requested\n      await placeUserInSpecificSide(referrerId, user.id, data.placementSide.toUpperCase() as 'LEFT' | 'RIGHT');\n    } else {\n      // Place user in weaker leg automatically\n      await placeUserInBinaryTree(referrerId, user.id);\n    }\n  }\n\n  return {\n    id: user.id,\n    email: user.email,\n    referralId: user.referralId,\n    kycStatus: user.kycStatus,\n  };\n};\n\n// User login\nexport const loginUser = async (data: {\n  email: string;\n  password: string;\n}) => {\n  const user = await userDb.findByEmail(data.email);\n  if (!user) {\n    throw new Error('Invalid email or password');\n  }\n\n  if (!user.isActive) {\n    throw new Error('Account is deactivated');\n  }\n\n  const isValidPassword = await verifyPassword(data.password, user.password);\n  if (!isValidPassword) {\n    throw new Error('Invalid email or password');\n  }\n\n  const token = generateToken({\n    userId: user.id,\n    email: user.email,\n  });\n\n  return {\n    token,\n    user: {\n      id: user.id,\n      email: user.email,\n      referralId: user.referralId,\n      kycStatus: user.kycStatus,\n    },\n  };\n};\n\n// Password validation\nexport const validatePassword = (password: string): { valid: boolean; errors: string[] } => {\n  const errors: string[] = [];\n\n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n\n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n\n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n\n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n\n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n\n  return {\n    valid: errors.length === 0,\n    errors,\n  };\n};\n\n// Email validation\nexport const validateEmail = (email: string): boolean => {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n};\n\n// Session management\nexport const createSession = (userId: string, email: string) => {\n  return generateToken({ userId, email });\n};\n\nexport const validateSession = (token: string) => {\n  return verifyToken(token);\n};\n\n// Admin authentication\nexport const isAdmin = async (userId: string): Promise<boolean> => {\n  const user = await userDb.findById(userId);\n  return user?.role === 'ADMIN';\n};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;AAEA;;;;AAEA,MAAM,aAAa,QAAQ,GAAG,CAAC,UAAU,IAAI;AAC7C,MAAM,iBAAiB,QAAQ,GAAG,CAAC,cAAc,IAAI;AAG9C,MAAM,eAAe,OAAO;IACjC,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;AACrC;AAEO,MAAM,iBAAiB,OAAO,UAAkB;IACrD,OAAO,MAAM,mIAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU;AACxC;AAGO,MAAM,gBAAgB,CAAC;IAC5B,OAAO,uIAAA,CAAA,UAAG,CAAC,IAAI,CAAC,SAAS,YAAY;QAAE,WAAW;IAAe;AACnE;AAEO,MAAM,cAAc,CAAC;IAC1B,IAAI;QACF,MAAM,UAAU,uIAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO;QAClC,OAAO;IACT,EAAE,OAAO,OAAO;QACd,OAAO;IACT;AACF;AAGO,MAAM,qBAAqB;IAChC,MAAM,QAAQ;IACd,IAAI,SAAS,MAAM,mBAAmB;IACtC,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,IAAK;QAC1B,UAAU,MAAM,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM;IAChE;IACA,OAAO;AACT;AAGO,MAAM,sBAAsB,OAAO;IACxC,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,kBAAkB,QAAQ,WAAW,OACzD,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IAEjD,IAAI,CAAC,OAAO;QACV,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,MAAM,UAAU,YAAY;IAC5B,IAAI,CAAC,SAAS;QACZ,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,KAAK;IACnD,IAAI,CAAC,QAAQ,CAAC,KAAK,QAAQ,EAAE;QAC3B,OAAO;YAAE,eAAe;YAAO,MAAM;QAAK;IAC5C;IAEA,OAAO;QAAE,eAAe;QAAM;IAAK;AACrC;AAGO,MAAM,eAAe,OAAO;IAQjC,+BAA+B;IAC/B,MAAM,eAAe,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,KAAK;IACxD,IAAI,cAAc;QAChB,MAAM,IAAI,MAAM;IAClB;IAEA,qCAAqC;IACrC,IAAI;IACJ,IAAI,KAAK,YAAY,EAAE;QACrB,MAAM,WAAW,MAAM,wHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC,KAAK,YAAY;QAChE,IAAI,CAAC,UAAU;YACb,MAAM,IAAI,MAAM;QAClB;QACA,aAAa,SAAS,EAAE;IAC1B;IAEA,gBAAgB;IAChB,MAAM,eAAe,MAAM,aAAa,KAAK,QAAQ;IAErD,8BAA8B;IAC9B,IAAI;IACJ,IAAI,WAAW;IACf,GAAG;QACD,aAAa;QACb,MAAM,WAAW,MAAM,wHAAA,CAAA,SAAM,CAAC,gBAAgB,CAAC;QAC/C,WAAW,CAAC;IACd,QAAS,CAAC,SAAU;IAIpB,4BAA4B;IAC5B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,MAAM,CAAC;QAC/B,OAAO,KAAK,KAAK;QACjB,WAAW,KAAK,SAAS;QACzB,UAAU,KAAK,QAAQ;QACvB,UAAU;QACV;IACF;IAEA,kDAAkD;IAClD,IAAI,YAAY;QACd,MAAM,EAAE,qBAAqB,EAAE,uBAAuB,EAAE,GAAG;QAE3D,IAAI,KAAK,aAAa,EAAE;YACtB,2CAA2C;YAC3C,MAAM,wBAAwB,YAAY,KAAK,EAAE,EAAE,KAAK,aAAa,CAAC,WAAW;QACnF,OAAO;YACL,yCAAyC;YACzC,MAAM,sBAAsB,YAAY,KAAK,EAAE;QACjD;IACF;IAEA,OAAO;QACL,IAAI,KAAK,EAAE;QACX,OAAO,KAAK,KAAK;QACjB,YAAY,KAAK,UAAU;QAC3B,WAAW,KAAK,SAAS;IAC3B;AACF;AAGO,MAAM,YAAY,OAAO;IAI9B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,KAAK,KAAK;IAChD,IAAI,CAAC,MAAM;QACT,MAAM,IAAI,MAAM;IAClB;IAEA,IAAI,CAAC,KAAK,QAAQ,EAAE;QAClB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,kBAAkB,MAAM,eAAe,KAAK,QAAQ,EAAE,KAAK,QAAQ;IACzE,IAAI,CAAC,iBAAiB;QACpB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,QAAQ,cAAc;QAC1B,QAAQ,KAAK,EAAE;QACf,OAAO,KAAK,KAAK;IACnB;IAEA,OAAO;QACL;QACA,MAAM;YACJ,IAAI,KAAK,EAAE;YACX,OAAO,KAAK,KAAK;YACjB,YAAY,KAAK,UAAU;YAC3B,WAAW,KAAK,SAAS;QAC3B;IACF;AACF;AAGO,MAAM,mBAAmB,CAAC;IAC/B,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,OAAO,OAAO,MAAM,KAAK;QACzB;IACF;AACF;AAGO,MAAM,gBAAgB,CAAC;IAC5B,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAGO,MAAM,gBAAgB,CAAC,QAAgB;IAC5C,OAAO,cAAc;QAAE;QAAQ;IAAM;AACvC;AAEO,MAAM,kBAAkB,CAAC;IAC9B,OAAO,YAAY;AACrB;AAGO,MAAM,UAAU,OAAO;IAC5B,MAAM,OAAO,MAAM,wHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC;IACnC,OAAO,MAAM,SAAS;AACxB", "debugId": null}}, {"offset": {"line": 630, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/mining.ts"], "sourcesContent": ["import { prisma } from './prisma';\nimport { miningUnitDb, transactionDb, systemLogDb } from './database';\n\n// Calculate daily ROI for all active mining units\nexport async function calculateDailyROI() {\n  try {\n    console.log('Starting daily ROI calculation...');\n\n    // Get all active mining units\n    const activeMiningUnits = await prisma.miningUnit.findMany({\n      where: {\n        status: 'ACTIVE',\n        expiryDate: {\n          gt: new Date(),\n        },\n      },\n      include: {\n        user: true,\n      },\n    });\n\n    console.log(`Found ${activeMiningUnits.length} active mining units`);\n\n    const results = [];\n\n    for (const unit of activeMiningUnits) {\n      try {\n        // Calculate daily earnings\n        const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;\n\n        // Check if unit should expire (5x investment earned)\n        const totalEarningsAfter = unit.totalEarned + dailyEarnings;\n        const maxEarnings = unit.investmentAmount * 5;\n\n        let finalEarnings = dailyEarnings;\n        let shouldExpire = false;\n\n        if (totalEarningsAfter >= maxEarnings) {\n          // Cap earnings at 5x investment\n          finalEarnings = maxEarnings - unit.totalEarned;\n          shouldExpire = true;\n        }\n\n        if (finalEarnings > 0) {\n          // Update mining unit total earned\n          await miningUnitDb.updateTotalEarned(unit.id, finalEarnings);\n\n          // Create pending earnings transaction\n          await transactionDb.create({\n            userId: unit.userId,\n            type: 'MINING_EARNINGS',\n            amount: finalEarnings,\n            description: `Daily mining earnings - ${unit.thsAmount} TH/s`,\n            status: 'PENDING',\n          });\n\n          results.push({\n            unitId: unit.id,\n            userId: unit.userId,\n            earnings: finalEarnings,\n            expired: shouldExpire,\n          });\n        }\n\n        // Expire unit if necessary\n        if (shouldExpire) {\n          await miningUnitDb.expireUnit(unit.id);\n          \n          await systemLogDb.create({\n            action: 'MINING_UNIT_EXPIRED',\n            userId: unit.userId,\n            details: {\n              miningUnitId: unit.id,\n              reason: '5x_investment_reached',\n              totalEarned: totalEarningsAfter,\n              investmentAmount: unit.investmentAmount,\n            },\n          });\n        }\n\n      } catch (unitError) {\n        console.error(`Error processing unit ${unit.id}:`, unitError);\n      }\n    }\n\n    // Log the daily ROI calculation\n    await systemLogDb.create({\n      action: 'DAILY_ROI_CALCULATED',\n      details: {\n        unitsProcessed: activeMiningUnits.length,\n        totalEarnings: results.reduce((sum, r) => sum + r.earnings, 0),\n        expiredUnits: results.filter(r => r.expired).length,\n        timestamp: new Date().toISOString(),\n      },\n    });\n\n    console.log(`Daily ROI calculation completed. Processed ${results.length} units.`);\n    return results;\n\n  } catch (error) {\n    console.error('Daily ROI calculation error:', error);\n    throw error;\n  }\n}\n\n// Process weekly earnings distribution (Saturday 15:00 UTC)\nexport async function processWeeklyEarnings() {\n  try {\n    console.log('Starting weekly earnings distribution...');\n\n    // Get all pending mining earnings\n    const pendingEarnings = await prisma.transaction.findMany({\n      where: {\n        type: 'MINING_EARNINGS',\n        status: 'PENDING',\n      },\n      include: {\n        user: true,\n      },\n    });\n\n    console.log(`Found ${pendingEarnings.length} pending earnings transactions`);\n\n    const userEarnings = new Map<string, number>();\n\n    // Group earnings by user\n    for (const transaction of pendingEarnings) {\n      const currentTotal = userEarnings.get(transaction.userId) || 0;\n      userEarnings.set(transaction.userId, currentTotal + transaction.amount);\n    }\n\n    const results = [];\n\n    // Process each user's earnings\n    for (const [userId, totalEarnings] of userEarnings) {\n      try {\n        // Mark all pending transactions as completed\n        await prisma.transaction.updateMany({\n          where: {\n            userId,\n            type: 'MINING_EARNINGS',\n            status: 'PENDING',\n          },\n          data: {\n            status: 'COMPLETED',\n          },\n        });\n\n        results.push({\n          userId,\n          totalEarnings,\n        });\n\n      } catch (userError) {\n        console.error(`Error processing earnings for user ${userId}:`, userError);\n      }\n    }\n\n    // Log the weekly distribution\n    await systemLogDb.create({\n      action: 'WEEKLY_EARNINGS_DISTRIBUTED',\n      details: {\n        usersProcessed: results.length,\n        totalDistributed: results.reduce((sum, r) => sum + r.totalEarnings, 0),\n        transactionsProcessed: pendingEarnings.length,\n        timestamp: new Date().toISOString(),\n      },\n    });\n\n    console.log(`Weekly earnings distribution completed. Processed ${results.length} users.`);\n    return results;\n\n  } catch (error) {\n    console.error('Weekly earnings distribution error:', error);\n    throw error;\n  }\n}\n\n// Check and expire mining units that have reached 12 months\nexport async function expireOldMiningUnits() {\n  try {\n    console.log('Checking for expired mining units...');\n\n    const expiredUnits = await prisma.miningUnit.findMany({\n      where: {\n        status: 'ACTIVE',\n        expiryDate: {\n          lte: new Date(),\n        },\n      },\n    });\n\n    console.log(`Found ${expiredUnits.length} units to expire`);\n\n    for (const unit of expiredUnits) {\n      await miningUnitDb.expireUnit(unit.id);\n      \n      await systemLogDb.create({\n        action: 'MINING_UNIT_EXPIRED',\n        userId: unit.userId,\n        details: {\n          miningUnitId: unit.id,\n          reason: '12_months_reached',\n          totalEarned: unit.totalEarned,\n          investmentAmount: unit.investmentAmount,\n        },\n      });\n    }\n\n    return expiredUnits.length;\n\n  } catch (error) {\n    console.error('Mining unit expiry check error:', error);\n    throw error;\n  }\n}\n\n// Get mining statistics\nexport async function getMiningStats() {\n  try {\n    const stats = await prisma.$transaction([\n      // Total TH/s sold\n      prisma.miningUnit.aggregate({\n        _sum: {\n          thsAmount: true,\n        },\n      }),\n      \n      // Active TH/s\n      prisma.miningUnit.aggregate({\n        where: {\n          status: 'ACTIVE',\n        },\n        _sum: {\n          thsAmount: true,\n        },\n      }),\n      \n      // Total investment\n      prisma.miningUnit.aggregate({\n        _sum: {\n          investmentAmount: true,\n        },\n      }),\n      \n      // Total earnings distributed\n      prisma.transaction.aggregate({\n        where: {\n          type: 'MINING_EARNINGS',\n          status: 'COMPLETED',\n        },\n        _sum: {\n          amount: true,\n        },\n      }),\n      \n      // Active mining units count\n      prisma.miningUnit.count({\n        where: {\n          status: 'ACTIVE',\n        },\n      }),\n      \n      // Total mining units count\n      prisma.miningUnit.count(),\n    ]);\n\n    return {\n      totalTHSSold: stats[0]._sum.thsAmount || 0,\n      activeTHS: stats[1]._sum.thsAmount || 0,\n      totalInvestment: stats[2]._sum.investmentAmount || 0,\n      totalEarningsDistributed: stats[3]._sum.amount || 0,\n      activeMiningUnits: stats[4],\n      totalMiningUnits: stats[5],\n    };\n\n  } catch (error) {\n    console.error('Mining stats error:', error);\n    throw error;\n  }\n}\n\n// Calculate user's estimated earnings\nexport async function calculateEstimatedEarnings(userId: string) {\n  try {\n    const activeMiningUnits = await miningUnitDb.findActiveByUserId(userId);\n    \n    if (activeMiningUnits.length === 0) {\n      return {\n        next7Days: 0,\n        next30Days: 0,\n        next365Days: 0,\n      };\n    }\n\n    let totalDaily = 0;\n    \n    for (const unit of activeMiningUnits) {\n      const dailyEarnings = (unit.investmentAmount * unit.dailyROI) / 100;\n      const maxEarnings = unit.investmentAmount * 5;\n      const remainingEarnings = maxEarnings - unit.totalEarned;\n      \n      // Use the lower of daily earnings or remaining earnings\n      totalDaily += Math.min(dailyEarnings, remainingEarnings);\n    }\n\n    return {\n      next7Days: totalDaily * 7,\n      next30Days: totalDaily * 30,\n      next365Days: totalDaily * 365,\n    };\n\n  } catch (error) {\n    console.error('Estimated earnings calculation error:', error);\n    throw error;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;;;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,8BAA8B;QAC9B,MAAM,oBAAoB,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACzD,OAAO;gBACL,QAAQ;gBACR,YAAY;oBACV,IAAI,IAAI;gBACV;YACF;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,kBAAkB,MAAM,CAAC,oBAAoB,CAAC;QAEnE,MAAM,UAAU,EAAE;QAElB,KAAK,MAAM,QAAQ,kBAAmB;YACpC,IAAI;gBACF,2BAA2B;gBAC3B,MAAM,gBAAgB,AAAC,KAAK,gBAAgB,GAAG,KAAK,QAAQ,GAAI;gBAEhE,qDAAqD;gBACrD,MAAM,qBAAqB,KAAK,WAAW,GAAG;gBAC9C,MAAM,cAAc,KAAK,gBAAgB,GAAG;gBAE5C,IAAI,gBAAgB;gBACpB,IAAI,eAAe;gBAEnB,IAAI,sBAAsB,aAAa;oBACrC,gCAAgC;oBAChC,gBAAgB,cAAc,KAAK,WAAW;oBAC9C,eAAe;gBACjB;gBAEA,IAAI,gBAAgB,GAAG;oBACrB,kCAAkC;oBAClC,MAAM,wHAAA,CAAA,eAAY,CAAC,iBAAiB,CAAC,KAAK,EAAE,EAAE;oBAE9C,sCAAsC;oBACtC,MAAM,wHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC;wBACzB,QAAQ,KAAK,MAAM;wBACnB,MAAM;wBACN,QAAQ;wBACR,aAAa,CAAC,wBAAwB,EAAE,KAAK,SAAS,CAAC,KAAK,CAAC;wBAC7D,QAAQ;oBACV;oBAEA,QAAQ,IAAI,CAAC;wBACX,QAAQ,KAAK,EAAE;wBACf,QAAQ,KAAK,MAAM;wBACnB,UAAU;wBACV,SAAS;oBACX;gBACF;gBAEA,2BAA2B;gBAC3B,IAAI,cAAc;oBAChB,MAAM,wHAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK,EAAE;oBAErC,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;wBACvB,QAAQ;wBACR,QAAQ,KAAK,MAAM;wBACnB,SAAS;4BACP,cAAc,KAAK,EAAE;4BACrB,QAAQ;4BACR,aAAa;4BACb,kBAAkB,KAAK,gBAAgB;wBACzC;oBACF;gBACF;YAEF,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,CAAC,sBAAsB,EAAE,KAAK,EAAE,CAAC,CAAC,CAAC,EAAE;YACrD;QACF;QAEA,gCAAgC;QAChC,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,SAAS;gBACP,gBAAgB,kBAAkB,MAAM;gBACxC,eAAe,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,QAAQ,EAAE;gBAC5D,cAAc,QAAQ,MAAM,CAAC,CAAA,IAAK,EAAE,OAAO,EAAE,MAAM;gBACnD,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,2CAA2C,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;QACjF,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,kCAAkC;QAClC,MAAM,kBAAkB,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,QAAQ,CAAC;YACxD,OAAO;gBACL,MAAM;gBACN,QAAQ;YACV;YACA,SAAS;gBACP,MAAM;YACR;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,gBAAgB,MAAM,CAAC,8BAA8B,CAAC;QAE3E,MAAM,eAAe,IAAI;QAEzB,yBAAyB;QACzB,KAAK,MAAM,eAAe,gBAAiB;YACzC,MAAM,eAAe,aAAa,GAAG,CAAC,YAAY,MAAM,KAAK;YAC7D,aAAa,GAAG,CAAC,YAAY,MAAM,EAAE,eAAe,YAAY,MAAM;QACxE;QAEA,MAAM,UAAU,EAAE;QAElB,+BAA+B;QAC/B,KAAK,MAAM,CAAC,QAAQ,cAAc,IAAI,aAAc;YAClD,IAAI;gBACF,6CAA6C;gBAC7C,MAAM,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,UAAU,CAAC;oBAClC,OAAO;wBACL;wBACA,MAAM;wBACN,QAAQ;oBACV;oBACA,MAAM;wBACJ,QAAQ;oBACV;gBACF;gBAEA,QAAQ,IAAI,CAAC;oBACX;oBACA;gBACF;YAEF,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,CAAC,mCAAmC,EAAE,OAAO,CAAC,CAAC,EAAE;YACjE;QACF;QAEA,8BAA8B;QAC9B,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;YACvB,QAAQ;YACR,SAAS;gBACP,gBAAgB,QAAQ,MAAM;gBAC9B,kBAAkB,QAAQ,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,aAAa,EAAE;gBACpE,uBAAuB,gBAAgB,MAAM;gBAC7C,WAAW,IAAI,OAAO,WAAW;YACnC;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,kDAAkD,EAAE,QAAQ,MAAM,CAAC,OAAO,CAAC;QACxF,OAAO;IAET,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,QAAQ,CAAC;YACpD,OAAO;gBACL,QAAQ;gBACR,YAAY;oBACV,KAAK,IAAI;gBACX;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,MAAM,EAAE,aAAa,MAAM,CAAC,gBAAgB,CAAC;QAE1D,KAAK,MAAM,QAAQ,aAAc;YAC/B,MAAM,wHAAA,CAAA,eAAY,CAAC,UAAU,CAAC,KAAK,EAAE;YAErC,MAAM,wHAAA,CAAA,cAAW,CAAC,MAAM,CAAC;gBACvB,QAAQ;gBACR,QAAQ,KAAK,MAAM;gBACnB,SAAS;oBACP,cAAc,KAAK,EAAE;oBACrB,QAAQ;oBACR,aAAa,KAAK,WAAW;oBAC7B,kBAAkB,KAAK,gBAAgB;gBACzC;YACF;QACF;QAEA,OAAO,aAAa,MAAM;IAE5B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM;IACR;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC;YACtC,kBAAkB;YAClB,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,MAAM;oBACJ,WAAW;gBACb;YACF;YAEA,cAAc;YACd,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,OAAO;oBACL,QAAQ;gBACV;gBACA,MAAM;oBACJ,WAAW;gBACb;YACF;YAEA,mBAAmB;YACnB,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,SAAS,CAAC;gBAC1B,MAAM;oBACJ,kBAAkB;gBACpB;YACF;YAEA,6BAA6B;YAC7B,sHAAA,CAAA,SAAM,CAAC,WAAW,CAAC,SAAS,CAAC;gBAC3B,OAAO;oBACL,MAAM;oBACN,QAAQ;gBACV;gBACA,MAAM;oBACJ,QAAQ;gBACV;YACF;YAEA,4BAA4B;YAC5B,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK,CAAC;gBACtB,OAAO;oBACL,QAAQ;gBACV;YACF;YAEA,2BAA2B;YAC3B,sHAAA,CAAA,SAAM,CAAC,UAAU,CAAC,KAAK;SACxB;QAED,OAAO;YACL,cAAc,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI;YACzC,WAAW,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,IAAI;YACtC,iBAAiB,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,gBAAgB,IAAI;YACnD,0BAA0B,KAAK,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,IAAI;YAClD,mBAAmB,KAAK,CAAC,EAAE;YAC3B,kBAAkB,KAAK,CAAC,EAAE;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uBAAuB;QACrC,MAAM;IACR;AACF;AAGO,eAAe,2BAA2B,MAAc;IAC7D,IAAI;QACF,MAAM,oBAAoB,MAAM,wHAAA,CAAA,eAAY,CAAC,kBAAkB,CAAC;QAEhE,IAAI,kBAAkB,MAAM,KAAK,GAAG;YAClC,OAAO;gBACL,WAAW;gBACX,YAAY;gBACZ,aAAa;YACf;QACF;QAEA,IAAI,aAAa;QAEjB,KAAK,MAAM,QAAQ,kBAAmB;YACpC,MAAM,gBAAgB,AAAC,KAAK,gBAAgB,GAAG,KAAK,QAAQ,GAAI;YAChE,MAAM,cAAc,KAAK,gBAAgB,GAAG;YAC5C,MAAM,oBAAoB,cAAc,KAAK,WAAW;YAExD,wDAAwD;YACxD,cAAc,KAAK,GAAG,CAAC,eAAe;QACxC;QAEA,OAAO;YACL,WAAW,aAAa;YACxB,YAAY,aAAa;YACzB,aAAa,aAAa;QAC5B;IAEF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yCAAyC;QACvD,MAAM;IACR;AACF", "debugId": null}}, {"offset": {"line": 906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/api/earnings/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server';\nimport { authenticateRequest } from '@/lib/auth';\nimport { transactionDb } from '@/lib/database';\nimport { calculateEstimatedEarnings } from '@/lib/mining';\n\n// GET - Fetch user's earnings data\nexport async function GET(request: NextRequest) {\n  try {\n    const { authenticated, user } = await authenticateRequest(request);\n\n    if (!authenticated || !user) {\n      return NextResponse.json(\n        { success: false, error: 'Not authenticated' },\n        { status: 401 }\n      );\n    }\n\n    // Get all earnings transactions\n    const earningsTransactions = await transactionDb.findByUserId(user.id, 100);\n    \n    // Filter by earnings types\n    const miningEarnings = earningsTransactions.filter(t => \n      t.type === 'MINING_EARNINGS' || \n      t.type === 'DIRECT_REFERRAL' || \n      t.type === 'BINARY_BONUS'\n    );\n\n    // Calculate totals\n    const totalEarnings = miningEarnings\n      .filter(t => t.status === 'COMPLETED')\n      .reduce((sum, t) => sum + t.amount, 0);\n\n    const pendingEarnings = miningEarnings\n      .filter(t => t.status === 'PENDING')\n      .reduce((sum, t) => sum + t.amount, 0);\n\n    const miningEarningsTotal = miningEarnings\n      .filter(t => t.type === 'MINING_EARNINGS' && t.status === 'COMPLETED')\n      .reduce((sum, t) => sum + t.amount, 0);\n\n    const referralEarningsTotal = miningEarnings\n      .filter(t => (t.type === 'DIRECT_REFERRAL' || t.type === 'BINARY_BONUS') && t.status === 'COMPLETED')\n      .reduce((sum, t) => sum + t.amount, 0);\n\n    // Get estimated future earnings\n    const estimatedEarnings = await calculateEstimatedEarnings(user.id);\n\n    // Get recent earnings (last 30 days)\n    const thirtyDaysAgo = new Date();\n    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);\n\n    const recentEarnings = miningEarnings.filter(t => \n      new Date(t.createdAt) >= thirtyDaysAgo && t.status === 'COMPLETED'\n    );\n\n    return NextResponse.json({\n      success: true,\n      data: {\n        totalEarnings,\n        pendingEarnings,\n        miningEarnings: miningEarningsTotal,\n        referralEarnings: referralEarningsTotal,\n        estimatedEarnings,\n        recentEarnings: recentEarnings.map(t => ({\n          id: t.id,\n          type: t.type,\n          amount: t.amount,\n          description: t.description,\n          createdAt: t.createdAt,\n        })),\n        earningsBreakdown: {\n          mining: miningEarningsTotal,\n          directReferral: miningEarnings\n            .filter(t => t.type === 'DIRECT_REFERRAL' && t.status === 'COMPLETED')\n            .reduce((sum, t) => sum + t.amount, 0),\n          binaryBonus: miningEarnings\n            .filter(t => t.type === 'BINARY_BONUS' && t.status === 'COMPLETED')\n            .reduce((sum, t) => sum + t.amount, 0),\n        },\n      },\n    });\n\n  } catch (error: any) {\n    console.error('Earnings fetch error:', error);\n    \n    return NextResponse.json(\n      { success: false, error: 'Failed to fetch earnings data' },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,aAAa,EAAE,IAAI,EAAE,GAAG,MAAM,CAAA,GAAA,oHAAA,CAAA,sBAAmB,AAAD,EAAE;QAE1D,IAAI,CAAC,iBAAiB,CAAC,MAAM;YAC3B,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAoB,GAC7C;gBAAE,QAAQ;YAAI;QAElB;QAEA,gCAAgC;QAChC,MAAM,uBAAuB,MAAM,wHAAA,CAAA,gBAAa,CAAC,YAAY,CAAC,KAAK,EAAE,EAAE;QAEvE,2BAA2B;QAC3B,MAAM,iBAAiB,qBAAqB,MAAM,CAAC,CAAA,IACjD,EAAE,IAAI,KAAK,qBACX,EAAE,IAAI,KAAK,qBACX,EAAE,IAAI,KAAK;QAGb,mBAAmB;QACnB,MAAM,gBAAgB,eACnB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,aACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,MAAM,kBAAkB,eACrB,MAAM,CAAC,CAAA,IAAK,EAAE,MAAM,KAAK,WACzB,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,MAAM,sBAAsB,eACzB,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,qBAAqB,EAAE,MAAM,KAAK,aACzD,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,MAAM,wBAAwB,eAC3B,MAAM,CAAC,CAAA,IAAK,CAAC,EAAE,IAAI,KAAK,qBAAqB,EAAE,IAAI,KAAK,cAAc,KAAK,EAAE,MAAM,KAAK,aACxF,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;QAEtC,gCAAgC;QAChC,MAAM,oBAAoB,MAAM,CAAA,GAAA,sHAAA,CAAA,6BAA0B,AAAD,EAAE,KAAK,EAAE;QAElE,qCAAqC;QACrC,MAAM,gBAAgB,IAAI;QAC1B,cAAc,OAAO,CAAC,cAAc,OAAO,KAAK;QAEhD,MAAM,iBAAiB,eAAe,MAAM,CAAC,CAAA,IAC3C,IAAI,KAAK,EAAE,SAAS,KAAK,iBAAiB,EAAE,MAAM,KAAK;QAGzD,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBACJ;gBACA;gBACA,gBAAgB;gBAChB,kBAAkB;gBAClB;gBACA,gBAAgB,eAAe,GAAG,CAAC,CAAA,IAAK,CAAC;wBACvC,IAAI,EAAE,EAAE;wBACR,MAAM,EAAE,IAAI;wBACZ,QAAQ,EAAE,MAAM;wBAChB,aAAa,EAAE,WAAW;wBAC1B,WAAW,EAAE,SAAS;oBACxB,CAAC;gBACD,mBAAmB;oBACjB,QAAQ;oBACR,gBAAgB,eACb,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,qBAAqB,EAAE,MAAM,KAAK,aACzD,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;oBACtC,aAAa,eACV,MAAM,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,kBAAkB,EAAE,MAAM,KAAK,aACtD,MAAM,CAAC,CAAC,KAAK,IAAM,MAAM,EAAE,MAAM,EAAE;gBACxC;YACF;QACF;IAEF,EAAE,OAAO,OAAY;QACnB,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAgC,GACzD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}