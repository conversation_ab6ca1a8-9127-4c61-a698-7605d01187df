'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { useRouter } from 'next/navigation';
import { DashboardLayout } from '@/components/dashboard/DashboardLayout';
import { DashboardOverview } from '@/components/dashboard/DashboardOverview';
import { PurchaseMiningUnit } from '@/components/dashboard/PurchaseMiningUnit';
import { EarningsTracker } from '@/components/dashboard/EarningsTracker';
import { WalletDashboard } from '@/components/dashboard/WalletDashboard';
import { BinaryTreeVisualizer } from '@/components/dashboard/BinaryTreeVisualizer';
import { KYCPortal } from '@/components/dashboard/KYCPortal';
import { MiningUnitsTable } from '@/components/dashboard/MiningUnitsTable';
import { SupportCenter } from '@/components/dashboard/SupportCenter';
import { Loading } from '@/components/ui';

export default function DashboardPage() {
  const { user, loading } = useAuth();
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (!loading && !user) {
      router.push('/login');
    }
  }, [user, loading, router]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Loading size="lg" text="Loading dashboard..." />
      </div>
    );
  }

  if (!user) {
    return null;
  }

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <DashboardOverview />;
      case 'mining':
        return (
          <div className="space-y-6">
            <PurchaseMiningUnit onPurchaseComplete={() => window.location.reload()} />
            <MiningUnitsTable />
          </div>
        );
      case 'earnings':
        return <EarningsTracker />;
      case 'wallet':
        return <WalletDashboard />;
      case 'referrals':
        return <BinaryTreeVisualizer />;
      case 'kyc':
        return <KYCPortal />;
      case 'support':
        return <SupportCenter />;
      case 'admin':
        // Show overview for admin users (they can access admin via separate link)
        return <DashboardOverview />;
      default:
        return <DashboardOverview />;
    }
  };

  return (
    <DashboardLayout activeTab={activeTab} onTabChange={setActiveTab}>
      {renderTabContent()}
    </DashboardLayout>
  );
}
