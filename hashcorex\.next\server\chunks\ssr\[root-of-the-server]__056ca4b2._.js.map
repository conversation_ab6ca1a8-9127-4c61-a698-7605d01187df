{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatNumber(num: number, decimals = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num);\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d);\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  if (navigator.clipboard) {\n    return navigator.clipboard.writeText(text);\n  }\n  \n  // Fallback for older browsers\n  const textArea = document.createElement('textarea');\n  textArea.value = text;\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  \n  try {\n    document.execCommand('copy');\n    return Promise.resolve();\n  } catch (err) {\n    return Promise.reject(err);\n  } finally {\n    document.body.removeChild(textArea);\n  }\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\nexport function calculateROI(\n  investment: number,\n  dailyRate: number,\n  days: number\n): number {\n  return investment * (dailyRate / 100) * days;\n}\n\nexport function calculateTHSPrice(ths: number, pricePerTHS: number): number {\n  return ths * pricePerTHS;\n}\n\nexport function formatTHS(ths: number): string {\n  if (ths >= 1000) {\n    return `${(ths / 1000).toFixed(1)}K TH/s`;\n  }\n  return `${ths.toFixed(2)} TH/s`;\n}\n\nexport function getTimeUntilNextPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n  \n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n  \n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n  \n  const diff = nextSaturday.getTime() - now.getTime();\n  \n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { days, hours, minutes, seconds };\n}\n\nexport function getTimeUntilBinaryPayout(): {\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextMidnight = new Date();\n  \n  // Set to next midnight UTC\n  nextMidnight.setUTCDate(now.getUTCDate() + 1);\n  nextMidnight.setUTCHours(0, 0, 0, 0);\n  \n  const diff = nextMidnight.getTime() - now.getTime();\n  \n  const hours = Math.floor(diff / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { hours, minutes, seconds };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW,EAAE,WAAW,CAAC;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,EAAE;QACvB,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;IACvC;IAEA,8BAA8B;IAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,KAAK,GAAG;IACjB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,SAAS,KAAK;IACd,SAAS,MAAM;IAEf,IAAI;QACF,SAAS,WAAW,CAAC;QACrB,OAAO,QAAQ,OAAO;IACxB,EAAE,OAAO,KAAK;QACZ,OAAO,QAAQ,MAAM,CAAC;IACxB,SAAU;QACR,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aACd,UAAkB,EAClB,SAAiB,EACjB,IAAY;IAEZ,OAAO,aAAa,CAAC,YAAY,GAAG,IAAI;AAC1C;AAEO,SAAS,kBAAkB,GAAW,EAAE,WAAmB;IAChE,OAAO,MAAM;AACf;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,OAAO,MAAM;QACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;IAC3C;IACA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;AACjC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC;AAEO,SAAS;IAKd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,2BAA2B;IAC3B,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK;IAC3C,aAAa,WAAW,CAAC,GAAG,GAAG,GAAG;IAElC,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;IAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAO;QAAS;IAAQ;AACnC", "debugId": null}}, {"offset": {"line": 218, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Container.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n}\n\nconst Container: React.FC<ContainerProps> = ({ \n  children, \n  className, \n  size = 'lg' \n}) => {\n  const sizeClasses = {\n    sm: 'max-w-2xl',\n    md: 'max-w-4xl',\n    lg: 'max-w-6xl',\n    xl: 'max-w-7xl',\n    full: 'max-w-full',\n  };\n\n  return (\n    <div\n      className={cn(\n        'mx-auto px-4 sm:px-6 lg:px-8',\n        sizeClasses[size],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Container };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACZ;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gCACA,WAAW,CAAC,KAAK,EACjB;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 250, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Grid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface GridProps {\n  children: React.ReactNode;\n  className?: string;\n  cols?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n  gap?: number;\n}\n\nconst Grid: React.FC<GridProps> = ({ \n  children, \n  className, \n  cols = { default: 1, md: 2, lg: 3 },\n  gap = 6\n}) => {\n  const getGridClasses = () => {\n    const classes = ['grid'];\n    \n    // Default columns\n    if (cols.default) {\n      classes.push(`grid-cols-${cols.default}`);\n    }\n    \n    // Responsive columns\n    if (cols.sm) {\n      classes.push(`sm:grid-cols-${cols.sm}`);\n    }\n    if (cols.md) {\n      classes.push(`md:grid-cols-${cols.md}`);\n    }\n    if (cols.lg) {\n      classes.push(`lg:grid-cols-${cols.lg}`);\n    }\n    if (cols.xl) {\n      classes.push(`xl:grid-cols-${cols.xl}`);\n    }\n    \n    // Gap\n    classes.push(`gap-${gap}`);\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getGridClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface GridItemProps {\n  children: React.ReactNode;\n  className?: string;\n  span?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n}\n\nconst GridItem: React.FC<GridItemProps> = ({ \n  children, \n  className, \n  span \n}) => {\n  const getSpanClasses = () => {\n    if (!span) return '';\n    \n    const classes = [];\n    \n    if (span.default) {\n      classes.push(`col-span-${span.default}`);\n    }\n    if (span.sm) {\n      classes.push(`sm:col-span-${span.sm}`);\n    }\n    if (span.md) {\n      classes.push(`md:col-span-${span.md}`);\n    }\n    if (span.lg) {\n      classes.push(`lg:col-span-${span.lg}`);\n    }\n    if (span.xl) {\n      classes.push(`xl:col-span-${span.xl}`);\n    }\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getSpanClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Grid, GridItem };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAkBA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,OAAO;IAAE,SAAS;IAAG,IAAI;IAAG,IAAI;AAAE,CAAC,EACnC,MAAM,CAAC,EACR;IACC,MAAM,iBAAiB;QACrB,MAAM,UAAU;YAAC;SAAO;QAExB,kBAAkB;QAClB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAC1C;QAEA,qBAAqB;QACrB,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QAEA,MAAM;QACN,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK;QAEzB,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP;AAcA,MAAM,WAAoC,CAAC,EACzC,QAAQ,EACR,SAAS,EACT,IAAI,EACL;IACC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,UAAU,EAAE;QAElB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,OAAO,EAAE;QACzC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QAEA,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Flex.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface FlexProps {\n  children: React.ReactNode;\n  className?: string;\n  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';\n  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';\n  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';\n  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';\n  gap?: number;\n}\n\nconst Flex: React.FC<FlexProps> = ({\n  children,\n  className,\n  direction = 'row',\n  align = 'start',\n  justify = 'start',\n  wrap = 'nowrap',\n  gap = 0,\n}) => {\n  const directionClasses = {\n    row: 'flex-row',\n    col: 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const alignClasses = {\n    start: 'items-start',\n    center: 'items-center',\n    end: 'items-end',\n    stretch: 'items-stretch',\n    baseline: 'items-baseline',\n  };\n\n  const justifyClasses = {\n    start: 'justify-start',\n    center: 'justify-center',\n    end: 'justify-end',\n    between: 'justify-between',\n    around: 'justify-around',\n    evenly: 'justify-evenly',\n  };\n\n  const wrapClasses = {\n    wrap: 'flex-wrap',\n    nowrap: 'flex-nowrap',\n    'wrap-reverse': 'flex-wrap-reverse',\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex',\n        directionClasses[direction],\n        alignClasses[align],\n        justifyClasses[justify],\n        wrapClasses[wrap],\n        gap > 0 && `gap-${gap}`,\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Flex };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAeA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,YAAY,KAAK,EACjB,QAAQ,OAAO,EACf,UAAU,OAAO,EACjB,OAAO,QAAQ,EACf,MAAM,CAAC,EACR;IACC,MAAM,mBAAmB;QACvB,KAAK;QACL,KAAK;QACL,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,UAAU;IACZ;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,MAAM;QACN,QAAQ;QACR,gBAAgB;IAClB;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,CAAC,UAAU,EAC3B,YAAY,CAAC,MAAM,EACnB,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,EACvB;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 386, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/index.ts"], "sourcesContent": ["export { Container } from './Container';\nexport { Grid, GridItem } from './Grid';\nexport { Flex } from './Flex';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 410, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/SolarPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const SolarPanel: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"2\" y1=\"8\" x2=\"22\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"16\" x2=\"22\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"6\" y1=\"4\" x2=\"6\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"4\" x2=\"10\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"4\" x2=\"14\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"4\" x2=\"18\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <circle cx=\"20\" cy=\"2\" r=\"1\" fill=\"currentColor\"/>\n      <path d=\"M19 1l1 1-1 1-1-1z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,aAAkC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACtE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACrE,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACrE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,8OAAC;gBAAK,GAAE;gBAAqB,MAAK;;;;;;;;;;;;AAGxC", "debugId": null}}, {"offset": {"line": 554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/MiningRig.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const MiningRig: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <rect x=\"4\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"4\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <circle cx=\"20\" cy=\"4\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"20\" y1=\"4\" x2=\"20\" y2=\"6\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"2\" x2=\"22\" y2=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"19\" y1=\"1\" x2=\"21\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n      <line x1=\"21\" y1=\"1\" x2=\"19\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,YAAiC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACrE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,8OAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG5E", "debugId": null}}, {"offset": {"line": 750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/Cryptocurrency.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Cryptocurrency: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h8M12 8v8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"12\" y1=\"6\" x2=\"12\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n\nexport const Bitcoin: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h4c1.1 0 2-.9 2-2s-.9-2-2-2H8v8h4c1.1 0 2-.9 2-2s-.9-2-2-2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"10\" y1=\"6\" x2=\"10\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"16\" x2=\"10\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"6\" x2=\"14\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"16\" x2=\"14\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAsC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAC1E,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,8OAAC;gBAAK,GAAE;gBAAiB,QAAO;gBAAe,aAAY;;;;;;0BAC3D,8OAAC;gBAAK,GAAE;gBAAsC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACzF,8OAAC;gBAAK,GAAE;gBAAuC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1F,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;AAEO,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,8OAAC;gBAAK,GAAE;gBAAkE,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrH,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E", "debugId": null}}, {"offset": {"line": 930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/EcoFriendly.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Leaf: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n\nexport const Recycle: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M7 19H4.815a1.83 1.83 0 01-1.57-.881 1.785 1.785 0 01-.004-1.784L7.196 9.5\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M11 19h8.203a1.83 1.83 0 001.556-.89 1.784 1.784 0 000-1.775l-1.226-2.12\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M14 16l-3 3 3 3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8.293 13.596L7.196 9.5l3.1 1.598\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M9.344 5.811L11.271 2a1.784 1.784 0 011.57-.881c.65 0 1.235.361 1.556.881l3.68 6.361\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M16 8l3-3-3-3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n    </svg>\n  );\n};\n\nexport const WindTurbine: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <line x1=\"12\" y1=\"12\" x2=\"12\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"10\" y1=\"22\" x2=\"14\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AASO,MAAM,OAA4B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAChE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAAwD,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3G,8OAAC;gBAAK,GAAE;gBAA8C,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACjG,8OAAC;gBAAK,GAAE;gBAA4C,MAAK;;;;;;;;;;;;AAG/D;AAEO,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,GAAE;gBAA6E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAChI,8OAAC;gBAAK,GAAE;gBAA2E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC9H,8OAAC;gBAAK,GAAE;gBAAkB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrE,8OAAC;gBAAK,GAAE;gBAAoC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACvF,8OAAC;gBAAK,GAAE;gBAAuF,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1I,8OAAC;gBAAK,GAAE;gBAAgB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;;;;;;;AAGzE;AAEO,MAAM,cAAmC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACvE,qBACE,8OAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,8OAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,8OAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;;;;;;0BACnC,8OAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E", "debugId": null}}, {"offset": {"line": 1144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/index.ts"], "sourcesContent": ["export { SolarPanel } from './SolarPanel';\nexport { MiningRig } from './MiningRig';\nexport { Cryptocurrency, Bitcoin } from './Cryptocurrency';\nexport { Leaf, Recycle, WindTurbine } from './EcoFriendly';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1171, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/AdminLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/hooks/useAuth';\nimport { Container, Flex } from '@/components/layout';\nimport { Button } from '@/components/ui';\nimport { SolarPanel } from '@/components/icons';\nimport { \n  LayoutDashboard, \n  Users, \n  Shield, \n  CreditCard, \n  Settings,\n  FileText,\n  LogOut,\n  Menu,\n  X,\n  ArrowLeft\n} from 'lucide-react';\n\ninterface AdminLayoutProps {\n  children: React.ReactNode;\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nexport const AdminLayout: React.FC<AdminLayoutProps> = ({\n  children,\n  activeTab,\n  onTabChange,\n}) => {\n  const { user, logout } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n\n  const navigationItems = [\n    { id: 'dashboard', label: 'Dashboard', icon: LayoutDashboard },\n    { id: 'users', label: 'User Management', icon: Users },\n    { id: 'kyc', label: 'KYC Review', icon: Shield },\n    { id: 'withdrawals', label: 'Withdrawals', icon: CreditCard },\n    { id: 'settings', label: 'System Settings', icon: Settings },\n    { id: 'logs', label: 'System Logs', icon: FileText },\n  ];\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div \n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Sidebar */}\n      <div className={`\n        fixed inset-y-0 left-0 z-50 w-64 bg-dark-900 shadow-lg transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:static lg:inset-0\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full'}\n      `}>\n        <div className=\"flex flex-col h-full\">\n          {/* Logo */}\n          <div className=\"flex items-center justify-between h-16 px-6 border-b border-gray-700\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <SolarPanel className=\"h-8 w-8 text-solar-400\" />\n              <span className=\"text-xl font-bold text-white\">HashCoreX Admin</span>\n            </Link>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden text-gray-400 hover:text-white\"\n            >\n              <X className=\"h-6 w-6\" />\n            </button>\n          </div>\n\n          {/* Admin info */}\n          <div className=\"px-6 py-4 border-b border-gray-700\">\n            <div className=\"flex items-center space-x-3\">\n              <div className=\"w-10 h-10 bg-solar-500 rounded-full flex items-center justify-center\">\n                <span className=\"text-white font-semibold\">\n                  {user?.email.charAt(0).toUpperCase()}\n                </span>\n              </div>\n              <div className=\"flex-1 min-w-0\">\n                <p className=\"text-sm font-medium text-white truncate\">\n                  {user?.email}\n                </p>\n                <p className=\"text-xs text-gray-400\">\n                  Administrator\n                </p>\n              </div>\n            </div>\n          </div>\n\n          {/* Navigation */}\n          <nav className=\"flex-1 px-4 py-4 space-y-2\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = activeTab === item.id;\n              \n              return (\n                <button\n                  key={item.id}\n                  onClick={() => {\n                    onTabChange(item.id);\n                    setSidebarOpen(false);\n                  }}\n                  className={`\n                    w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-left transition-colors\n                    ${isActive \n                      ? 'bg-solar-500 text-white' \n                      : 'text-gray-300 hover:bg-gray-800 hover:text-white'\n                    }\n                  `}\n                >\n                  <Icon className=\"h-5 w-5\" />\n                  <span className=\"font-medium\">{item.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n\n          {/* Footer */}\n          <div className=\"px-4 py-4 border-t border-gray-700 space-y-2\">\n            <Link\n              href=\"/dashboard\"\n              className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors\"\n            >\n              <ArrowLeft className=\"h-5 w-5\" />\n              <span className=\"font-medium\">Back to Dashboard</span>\n            </Link>\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-300 hover:bg-gray-800 hover:text-white transition-colors\"\n            >\n              <LogOut className=\"h-5 w-5\" />\n              <span className=\"font-medium\">Logout</span>\n            </button>\n          </div>\n        </div>\n      </div>\n\n      {/* Main content */}\n      <div className=\"lg:pl-64\">\n        {/* Top bar */}\n        <header className=\"bg-white shadow-sm border-b border-gray-200\">\n          <div className=\"px-4 sm:px-6 lg:px-8\">\n            <Flex justify=\"between\" align=\"center\" className=\"h-16\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => setSidebarOpen(true)}\n                  className=\"lg:hidden text-gray-500 hover:text-gray-700\"\n                >\n                  <Menu className=\"h-6 w-6\" />\n                </button>\n                <h1 className=\"text-xl font-semibold text-dark-900 capitalize\">\n                  {navigationItems.find(item => item.id === activeTab)?.label || 'Admin Panel'}\n                </h1>\n              </div>\n\n              <div className=\"flex items-center space-x-4\">\n                <div className=\"px-3 py-1 bg-red-100 text-red-700 rounded-full text-xs font-medium\">\n                  ADMIN MODE\n                </div>\n              </div>\n            </Flex>\n          </div>\n        </header>\n\n        {/* Page content */}\n        <main className=\"p-4 sm:p-6 lg:p-8\">\n          <Container size=\"full\">\n            {children}\n          </Container>\n        </main>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAEA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AARA;;;;;;;;AA2BO,MAAM,cAA0C,CAAC,EACtD,QAAQ,EACR,SAAS,EACT,WAAW,EACZ;IACC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,kBAAkB;QACtB;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,4NAAA,CAAA,kBAAe;QAAC;QAC7D;YAAE,IAAI;YAAS,OAAO;YAAmB,MAAM,oMAAA,CAAA,QAAK;QAAC;QACrD;YAAE,IAAI;YAAO,OAAO;YAAc,MAAM,sMAAA,CAAA,SAAM;QAAC;QAC/C;YAAE,IAAI;YAAe,OAAO;YAAe,MAAM,kNAAA,CAAA,aAAU;QAAC;QAC5D;YAAE,IAAI;YAAY,OAAO;YAAmB,MAAM,0MAAA,CAAA,WAAQ;QAAC;QAC3D;YAAE,IAAI;YAAQ,OAAO;YAAe,MAAM,8MAAA,CAAA,WAAQ;QAAC;KACpD;IAED,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,qBACE,8OAAC;QAAI,WAAU;;YAEZ,6BACC,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,8OAAC;gBAAI,WAAW,CAAC;;QAEf,EAAE,cAAc,kBAAkB,oBAAoB;MACxD,CAAC;0BACC,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,8OAAC,yIAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,8OAAC;4CAAK,WAAU;sDAA+B;;;;;;;;;;;;8CAEjD,8OAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAKjB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAK,WAAU;sDACb,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;kDAG3B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DACV,MAAM;;;;;;0DAET,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;sCAQ3C,8OAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,cAAc,KAAK,EAAE;gCAEtC,qBACE,8OAAC;oCAEC,SAAS;wCACP,YAAY,KAAK,EAAE;wCACnB,eAAe;oCACjB;oCACA,WAAW,CAAC;;oBAEV,EAAE,WACE,4BACA,mDACH;kBACH,CAAC;;sDAED,8OAAC;4CAAK,WAAU;;;;;;sDAChB,8OAAC;4CAAK,WAAU;sDAAe,KAAK,KAAK;;;;;;;mCAdpC,KAAK,EAAE;;;;;4BAiBlB;;;;;;sCAIF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAK;oCACL,WAAU;;sDAEV,8OAAC,gNAAA,CAAA,YAAS;4CAAC,WAAU;;;;;;sDACrB,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;8CAEhC,8OAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAK,WAAU;sDAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAOtC,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAO,WAAU;kCAChB,cAAA,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,oIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAU,OAAM;gCAAS,WAAU;;kDAC/C,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,8OAAC;gDAAG,WAAU;0DACX,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,SAAS;;;;;;;;;;;;kDAInE,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC;4CAAI,WAAU;sDAAqE;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS5F,8OAAC;wBAAK,WAAU;kCACd,cAAA,8OAAC,yIAAA,CAAA,YAAS;4BAAC,MAAK;sCACb;;;;;;;;;;;;;;;;;;;;;;;AAMb", "debugId": null}}, {"offset": {"line": 1584, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl',\n  {\n    variants: {\n      variant: {\n        primary: 'bg-gradient-to-r from-solar-500 to-solar-600 text-white hover:from-solar-600 hover:to-solar-700 focus:ring-solar-500 animate-pulse-glow',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 hover:from-gray-200 hover:to-gray-300 focus:ring-gray-500',\n        success: 'bg-gradient-to-r from-eco-500 to-eco-600 text-white hover:from-eco-600 hover:to-eco-700 focus:ring-eco-500',\n        danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500',\n        outline: 'border-2 border-solar-500 bg-transparent text-solar-600 hover:bg-solar-500 hover:text-white focus:ring-solar-500 backdrop-blur-sm',\n        ghost: 'text-gray-600 hover:bg-solar-50 hover:text-solar-700 focus:ring-solar-500 rounded-lg',\n        link: 'text-solar-600 underline-offset-4 hover:underline focus:ring-solar-500 hover:text-solar-700',\n        premium: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 focus:ring-purple-500',\n        glass: 'glass-morphism text-dark-900 hover:bg-white/20 backdrop-blur-xl border border-white/20',\n      },\n      size: {\n        sm: 'h-10 px-4 text-sm rounded-lg',\n        md: 'h-12 px-6 text-base rounded-xl',\n        lg: 'h-14 px-8 text-lg rounded-xl',\n        xl: 'h-16 px-10 text-xl rounded-2xl font-bold',\n        icon: 'h-12 w-12 rounded-xl',\n      },\n    },\n    defaultVariants: {\n      variant: 'primary',\n      size: 'md',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <div className=\"mr-2\">\n            <div className=\"spinner\" />\n          </div>\n        )}\n        {leftIcon && !loading && <span className=\"mr-2\">{leftIcon}</span>}\n        {children}\n        {rightIcon && !loading && <span className=\"ml-2\">{rightIcon}</span>}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,yQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;YACR,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;YAGlB,YAAY,CAAC,yBAAW,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD;YACA,aAAa,CAAC,yBAAW,8OAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1680, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\nexport interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardHeader.displayName = 'CardHeader';\n\nexport interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode;\n}\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <h3\n        ref={ref}\n        className={cn('text-xl font-semibold leading-none tracking-tight text-dark-900', className)}\n        {...props}\n      >\n        {children}\n      </h3>\n    );\n  }\n);\n\nCardTitle.displayName = 'CardTitle';\n\nexport interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode;\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn('text-sm text-gray-500', className)}\n        {...props}\n      >\n        {children}\n      </p>\n    );\n  }\n);\n\nCardDescription.displayName = 'CardDescription';\n\nexport interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardContent.displayName = 'CardContent';\n\nexport interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex items-center p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AASA,MAAM,qBAAO,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0KACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,KAAK,WAAW,GAAG;AAMnB,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,WAAW,WAAW,GAAG;AAMzB,MAAM,0BAAY,qMAAA,CAAA,UAAK,CAAC,UAAU,CAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,UAAU,WAAW,GAAG;AAMxB,MAAM,gCAAkB,qMAAA,CAAA,UAAK,CAAC,UAAU,CACtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,gBAAgB,WAAW,GAAG;AAM9B,MAAM,4BAAc,qMAAA,CAAA,UAAK,CAAC,UAAU,CAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,YAAY,WAAW,GAAG;AAM1B,MAAM,2BAAa,qMAAA,CAAA,UAAK,CAAC,UAAU,CACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAGP;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1780, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, leftIcon, rightIcon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400\">{leftIcon}</span>\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              'flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300',\n              leftIcon && 'pl-12',\n              rightIcon && 'pr-12',\n              error && 'border-red-500 focus:ring-red-500 focus:border-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <span className=\"text-gray-400\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,sBAAQ,qMAAA,CAAA,UAAK,CAAC,UAAU,CAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACjE,qBACE,8OAAC;QAAI,WAAU;;YACZ,uBACC,8OAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,8OAAC;gBAAI,WAAU;;oBACZ,0BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;kCAGrC,8OAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8UACA,YAAY,SACZ,aAAa,SACb,SAAS,0DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;YAItC,uBACC,8OAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1874, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './Button';\n\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showCloseButton?: boolean;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n}) => {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n  };\n\n  const modalContent = (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div\n        className={cn(\n          'relative w-full bg-white rounded-xl shadow-xl transform transition-all',\n          sizeClasses[size]\n        )}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-dark-900\">{title}</h2>\n          {showCloseButton && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onClose}\n              className=\"h-8 w-8 rounded-full\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n        \n        {/* Content */}\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\nexport { Modal };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AANA;;;;;;;AAiBA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACvB;IACC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,QAAQ;YACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;KAAO;IAEX,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,CAAC;YACpB,IAAI,MAAM,GAAG,KAAK,UAAU;gBAC1B;YACF;QACF;QAEA,IAAI,QAAQ;YACV,SAAS,gBAAgB,CAAC,WAAW;QACvC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;QAC1C;IACF,GAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,6BACJ,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0EACA,WAAW,CAAC,KAAK;gBAEnB,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAuC;;;;;;4BACpD,iCACC,8OAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,8OAAC,4LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMnB,8OAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;IAMT,qBAAO,CAAA,GAAA,4MAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD", "debugId": null}}, {"offset": {"line": 2004, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ size = 'md', className, text }) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  };\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-gray-300 border-t-solar-500',\n          sizeClasses[size]\n        )}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport interface LoadingOverlayProps {\n  isLoading: boolean;\n  text?: string;\n  children: React.ReactNode;\n}\n\nconst LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  isLoading,\n  text = 'Loading...',\n  children,\n}) => {\n  return (\n    <div className=\"relative\">\n      {children}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10\">\n          <Loading text={text} />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport { Loading, LoadingOverlay };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAWA,MAAM,UAAkC,CAAC,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;IACvE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yEACA,WAAW,CAAC,KAAK;;;;;;YAGpB,sBACC,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;AAQA,MAAM,iBAAgD,CAAC,EACrD,SAAS,EACT,OAAO,YAAY,EACnB,QAAQ,EACT;IACC,qBACE,8OAAC;QAAI,WAAU;;YACZ;YACA,2BACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAQ,MAAM;;;;;;;;;;;;;;;;;AAKzB", "debugId": null}}, {"offset": {"line": 2077, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/index.ts"], "sourcesContent": ["export { Button, buttonVariants, type ButtonProps } from './Button';\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card';\nexport { Input, type InputProps } from './Input';\nexport { Modal, type ModalProps } from './Modal';\nexport { Loading, LoadingOverlay, type LoadingProps } from './Loading';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2107, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/admin/AdminDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { \n  Users, \n  DollarSign, \n  TrendingUp, \n  Zap, \n  Shield, \n  CreditCard,\n  AlertTriangle,\n  CheckCircle\n} from 'lucide-react';\nimport { formatCurrency, formatNumber, formatTHS } from '@/lib/utils';\n\ninterface AdminStats {\n  totalUsers: number;\n  activeUsers: number;\n  pendingKYC: number;\n  approvedKYC: number;\n  totalInvestments: number;\n  totalEarningsDistributed: number;\n  totalTHSSold: number;\n  activeTHS: number;\n  pendingWithdrawals: number;\n  totalWithdrawals: number;\n  platformRevenue: number;\n  binaryPoolBalance: number;\n}\n\nexport const AdminDashboard: React.FC = () => {\n  const [stats, setStats] = useState<AdminStats | null>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchAdminStats();\n  }, []);\n\n  const fetchAdminStats = async () => {\n    try {\n      const response = await fetch('/api/admin/stats', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setStats(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch admin stats:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {Array.from({ length: 4 }).map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (!stats) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load admin statistics</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Welcome Section */}\n      <div className=\"bg-gradient-to-r from-dark-900 to-gray-800 rounded-xl p-6 text-white\">\n        <h1 className=\"text-2xl font-bold mb-2\">Admin Dashboard</h1>\n        <p className=\"text-gray-300\">\n          Monitor platform performance, manage users, and oversee all operations.\n        </p>\n      </div>\n\n      {/* User Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-dark-900 mb-4\">User Management</h2>\n        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Users</p>\n                  <p className=\"text-2xl font-bold text-dark-900\">\n                    {formatNumber(stats.totalUsers, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <Users className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active Users</p>\n                  <p className=\"text-2xl font-bold text-eco-600\">\n                    {formatNumber(stats.activeUsers, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-eco-100 rounded-full flex items-center justify-center\">\n                  <CheckCircle className=\"h-6 w-6 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending KYC</p>\n                  <p className=\"text-2xl font-bold text-solar-600\">\n                    {formatNumber(stats.pendingKYC, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-solar-100 rounded-full flex items-center justify-center\">\n                  <Shield className=\"h-6 w-6 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Approved KYC</p>\n                  <p className=\"text-2xl font-bold text-dark-900\">\n                    {formatNumber(stats.approvedKYC, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center\">\n                  <Shield className=\"h-6 w-6 text-gray-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Financial Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-dark-900 mb-4\">Financial Overview</h2>\n        <Grid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Investments</p>\n                  <p className=\"text-2xl font-bold text-eco-600\">\n                    {formatCurrency(stats.totalInvestments)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-eco-100 rounded-full flex items-center justify-center\">\n                  <DollarSign className=\"h-6 w-6 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Earnings Distributed</p>\n                  <p className=\"text-2xl font-bold text-blue-600\">\n                    {formatCurrency(stats.totalEarningsDistributed)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-blue-100 rounded-full flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Platform Revenue</p>\n                  <p className=\"text-2xl font-bold text-purple-600\">\n                    {formatCurrency(stats.platformRevenue)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-purple-100 rounded-full flex items-center justify-center\">\n                  <DollarSign className=\"h-6 w-6 text-purple-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Binary Pool</p>\n                  <p className=\"text-2xl font-bold text-solar-600\">\n                    {formatCurrency(stats.binaryPoolBalance)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-solar-100 rounded-full flex items-center justify-center\">\n                  <TrendingUp className=\"h-6 w-6 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Mining Statistics */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-dark-900 mb-4\">Mining Operations</h2>\n        <Grid cols={{ default: 1, md: 2 }} gap={6}>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total TH/s Sold</p>\n                  <p className=\"text-2xl font-bold text-solar-600\">\n                    {formatTHS(stats.totalTHSSold)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-solar-100 rounded-full flex items-center justify-center\">\n                  <Zap className=\"h-6 w-6 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Active TH/s</p>\n                  <p className=\"text-2xl font-bold text-eco-600\">\n                    {formatTHS(stats.activeTHS)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-eco-100 rounded-full flex items-center justify-center\">\n                  <Zap className=\"h-6 w-6 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Withdrawal Management */}\n      <div>\n        <h2 className=\"text-lg font-semibold text-dark-900 mb-4\">Withdrawal Management</h2>\n        <Grid cols={{ default: 1, md: 2 }} gap={6}>\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Pending Withdrawals</p>\n                  <p className=\"text-2xl font-bold text-solar-600\">\n                    {formatNumber(stats.pendingWithdrawals, 0)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-solar-100 rounded-full flex items-center justify-center\">\n                  <AlertTriangle className=\"h-6 w-6 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card>\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600\">Total Withdrawals</p>\n                  <p className=\"text-2xl font-bold text-dark-900\">\n                    {formatCurrency(stats.totalWithdrawals)}\n                  </p>\n                </div>\n                <div className=\"h-12 w-12 bg-gray-100 rounded-full flex items-center justify-center\">\n                  <CreditCard className=\"h-6 w-6 text-gray-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Quick Actions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Quick Actions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          <Grid cols={{ default: 1, md: 3 }} gap={4}>\n            <div className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\">\n              <div className=\"flex items-center space-x-3\">\n                <Shield className=\"h-8 w-8 text-solar-500\" />\n                <div>\n                  <h3 className=\"font-medium text-dark-900\">Review KYC</h3>\n                  <p className=\"text-sm text-gray-600\">{stats.pendingKYC} pending reviews</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\">\n              <div className=\"flex items-center space-x-3\">\n                <CreditCard className=\"h-8 w-8 text-eco-500\" />\n                <div>\n                  <h3 className=\"font-medium text-dark-900\">Process Withdrawals</h3>\n                  <p className=\"text-sm text-gray-600\">{stats.pendingWithdrawals} pending</p>\n                </div>\n              </div>\n            </div>\n\n            <div className=\"p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors\">\n              <div className=\"flex items-center space-x-3\">\n                <Users className=\"h-8 w-8 text-blue-500\" />\n                <div>\n                  <h3 className=\"font-medium text-dark-900\">Manage Users</h3>\n                  <p className=\"text-sm text-gray-600\">{stats.totalUsers} total users</p>\n                </div>\n              </div>\n            </div>\n          </Grid>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAfA;;;;;;;AAgCO,MAAM,iBAA2B;IACtC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAqB;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,SAAS,KAAK,IAAI;gBACpB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,8OAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,8OAAC;oBAAY,WAAU;8BACrB,cAAA,8OAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,8OAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,8OAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;0BAM/B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU,EAAE;;;;;;;;;;;;0DAGpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,oMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,2NAAA,CAAA,cAAW;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM/B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,UAAU,EAAE;;;;;;;;;;;;0DAGpC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,WAAW,EAAE;;;;;;;;;;;;0DAGrC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,wBAAwB;;;;;;;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;;;;;;;0DAGzC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB;;;;;;;;;;;;0DAG3C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,YAAY;;;;;;;;;;;;0DAGjC,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMvB,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,SAAS;;;;;;;;;;;;0DAG9B,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,gMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS3B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2C;;;;;;kCACzD,8OAAC,oIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,eAAY,AAAD,EAAE,MAAM,kBAAkB,EAAE;;;;;;;;;;;;0DAG5C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMjC,8OAAC,gIAAA,CAAA,OAAI;0CACH,cAAA,8OAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAE,WAAU;kEAAoC;;;;;;kEACjD,8OAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,mHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,gBAAgB;;;;;;;;;;;;0DAG1C,8OAAC;gDAAI,WAAU;0DACb,cAAA,8OAAC,kNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASlC,8OAAC,gIAAA,CAAA,OAAI;;kCACH,8OAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,8OAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,8OAAC,gIAAA,CAAA,cAAW;kCACV,cAAA,8OAAC,oIAAA,CAAA,OAAI;4BAAC,MAAM;gCAAE,SAAS;gCAAG,IAAI;4BAAE;4BAAG,KAAK;;8CACtC,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,sMAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;;4DAAyB,MAAM,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAK7D,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,kNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;0DACtB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;;4DAAyB,MAAM,kBAAkB;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;8CAKrE,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,oMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,8OAAC;;kEACC,8OAAC;wDAAG,WAAU;kEAA4B;;;;;;kEAC1C,8OAAC;wDAAE,WAAU;;4DAAyB,MAAM,UAAU;4DAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASzE", "debugId": null}}, {"offset": {"line": 3272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/%28admin%29/admin/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useRouter } from 'next/navigation';\nimport { AdminLayout } from '@/components/admin/AdminLayout';\nimport { AdminDashboard } from '@/components/admin/AdminDashboard';\nimport { UserManagement } from '@/components/admin/UserManagement';\nimport { KYCReview } from '@/components/admin/KYCReview';\nimport { WithdrawalManagement } from '@/components/admin/WithdrawalManagement';\nimport { SystemSettings } from '@/components/admin/SystemSettings';\nimport { SystemLogs } from '@/components/admin/SystemLogs';\nimport { Loading } from '@/components/ui';\nimport { isAdmin } from '@/lib/auth';\n\nexport default function AdminPage() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState('dashboard');\n  const [isAdminUser, setIsAdminUser] = useState(false);\n  const [checkingAdmin, setCheckingAdmin] = useState(true);\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n      return;\n    }\n\n    if (user) {\n      checkAdminStatus();\n    }\n  }, [user, loading, router]);\n\n  const checkAdminStatus = async () => {\n    try {\n      const response = await fetch('/api/admin/check', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        setIsAdminUser(data.isAdmin);\n        \n        if (!data.isAdmin) {\n          router.push('/dashboard');\n        }\n      } else {\n        router.push('/dashboard');\n      }\n    } catch (error) {\n      console.error('Admin check failed:', error);\n      router.push('/dashboard');\n    } finally {\n      setCheckingAdmin(false);\n    }\n  };\n\n  if (loading || checkingAdmin) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Loading size=\"lg\" text=\"Loading admin panel...\" />\n      </div>\n    );\n  }\n\n  if (!user || !isAdminUser) {\n    return null;\n  }\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'dashboard':\n        return <AdminDashboard />;\n      case 'users':\n        return <UserManagement />;\n      case 'kyc':\n        return <KYCReview />;\n      case 'withdrawals':\n        return <WithdrawalManagement />;\n      case 'settings':\n        return <SystemSettings />;\n      case 'logs':\n        return <SystemLogs />;\n      default:\n        return <AdminDashboard />;\n    }\n  };\n\n  return (\n    <AdminLayout activeTab={activeTab} onTabChange={setActiveTab}>\n      {renderTabContent()}\n    </AdminLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AAAA;AAZA;;;;;;;;;;;;;AAee,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,MAAM;YACrB,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,IAAI,MAAM;YACR;QACF;IACF,GAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,oBAAoB;gBAC/C,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,eAAe,KAAK,OAAO;gBAE3B,IAAI,CAAC,KAAK,OAAO,EAAE;oBACjB,OAAO,IAAI,CAAC;gBACd;YACF,OAAO;gBACL,OAAO,IAAI,CAAC;YACd;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,OAAO,IAAI,CAAC;QACd,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,IAAI,WAAW,eAAe;QAC5B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,mIAAA,CAAA,UAAO;gBAAC,MAAK;gBAAK,MAAK;;;;;;;;;;;IAG9B;IAEA,IAAI,CAAC,QAAQ,CAAC,aAAa;QACzB,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,8OAAC,6IAAA,CAAA,iBAAc;;;;;YACxB,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV,KAAK;gBACH,qBAAO,8OAAC;;;;;YACV;gBACE,qBAAO,8OAAC,6IAAA,CAAA,iBAAc;;;;;QAC1B;IACF;IAEA,qBACE,8OAAC,0IAAA,CAAA,cAAW;QAAC,WAAW;QAAW,aAAa;kBAC7C;;;;;;AAGP", "debugId": null}}]}