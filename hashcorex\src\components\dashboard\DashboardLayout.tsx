'use client';

import React, { useState, useRef, useEffect } from 'react';
import Link from 'next/link';
import { useAuth } from '@/hooks/useAuth';
import { Container, Flex } from '@/components/layout';
import { Button } from '@/components/ui';
import { SolarPanel } from '@/components/icons';
import {
  LayoutDashboard,
  Zap,
  TrendingUp,
  Wallet,
  Users,
  Shield,
  Settings,
  LogOut,
  Menu,
  X,
  Bell,
  AlertCircle,
  ChevronDown,
  User,
  CreditCard,
  MessageCircle
} from 'lucide-react';

interface DashboardLayoutProps {
  children: React.ReactNode;
  activeTab: string;
  onTabChange: (tab: string) => void;
}

export const DashboardLayout: React.FC<DashboardLayoutProps> = ({
  children,
  activeTab,
  onTabChange,
}) => {
  const { user, logout } = useAuth();
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [kycBannerDismissed, setKycBannerDismissed] = useState(false);
  const [userDropdownOpen, setUserDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Base navigation items
  const baseNavigationItems = [
    { id: 'overview', label: 'Overview', icon: LayoutDashboard },
    { id: 'mining', label: 'Mining Units', icon: Zap },
    { id: 'earnings', label: 'Earnings', icon: TrendingUp },
    { id: 'wallet', label: 'Wallet', icon: Wallet },
    { id: 'referrals', label: 'Referrals', icon: Users },
    { id: 'kyc', label: 'KYC Verification', icon: Shield },
    { id: 'support', label: 'Support', icon: MessageCircle },
  ];

  // Add admin panel for admin users
  const navigationItems = user?.role === 'ADMIN'
    ? [...baseNavigationItems, { id: 'admin', label: 'Admin Panel', icon: Settings }]
    : baseNavigationItems;

  const handleLogout = async () => {
    await logout();
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setUserDropdownOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Fixed Sidebar */}
      <aside className={`
        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200
        transform transition-all duration-300 ease-in-out
        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <div className="flex flex-col h-screen">
          {/* Logo Header */}
          <div className="flex items-center justify-between h-14 px-5 border-b border-gray-200 bg-white flex-shrink-0">
            <Link href="/" className="flex items-center space-x-2">
              <div className="w-8 h-8 bg-gradient-to-br from-solar-500 to-eco-500 rounded-lg flex items-center justify-center">
                <SolarPanel className="h-5 w-5 text-white" />
              </div>
              <span className="text-lg font-bold text-gray-900">HashCoreX</span>
            </Link>
            <button
              onClick={() => setSidebarOpen(false)}
              className="lg:hidden p-1.5 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
            >
              <X className="h-4 w-4" />
            </button>
          </div>



          {/* Navigation Menu */}
          <nav className="flex-1 px-3 py-4 space-y-1 min-h-0">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const isActive = activeTab === item.id;

              return (
                <button
                  key={item.id}
                  onClick={() => {
                    onTabChange(item.id);
                    setSidebarOpen(false);
                  }}
                  className={`
                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 group
                    ${isActive
                      ? 'bg-gradient-to-r from-solar-500 to-eco-500 text-white shadow-md'
                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'
                    }
                  `}
                >
                  <Icon className={`h-4 w-4 ${isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'}`} />
                  <span className="font-medium text-sm">{item.label}</span>
                </button>
              );
            })}
          </nav>

          {/* Sidebar Footer */}
          <div className="px-3 py-3 border-t border-gray-200 bg-gray-50 flex-shrink-0">
            <button
              onClick={handleLogout}
              className="w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-gray-600 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group"
            >
              <LogOut className="h-4 w-4 group-hover:text-red-600" />
              <span className="font-medium text-sm">Logout</span>
            </button>
          </div>
        </div>
      </aside>

      {/* Main Content Area */}
      <div className="flex-1 flex flex-col min-w-0 lg:ml-64">
        {/* Top Navigation Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30">
          <div className="px-4 sm:px-6 lg:px-8 xl:px-12">
            <Flex justify="between" align="center" className="h-16">
              <div className="flex items-center space-x-4">
                <button
                  onClick={() => setSidebarOpen(true)}
                  className="lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors"
                >
                  <Menu className="h-6 w-6" />
                </button>
                <div>
                  <h1 className="text-xl font-bold text-gray-900 capitalize">
                    {navigationItems.find(item => item.id === activeTab)?.label || 'Dashboard'}
                  </h1>
                  <p className="text-sm text-gray-500 hidden sm:block">
                    Manage your mining operations and earnings
                  </p>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                {/* Notifications */}
                <button className="relative p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors">
                  <Bell className="h-5 w-5" />
                  <span className="absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full"></span>
                </button>

                {/* KYC Status - Only show if not approved and on relevant pages */}
                {user?.kycStatus !== 'APPROVED' && (activeTab === 'kyc' || activeTab === 'wallet') && (
                  <div className={`
                    px-3 py-1.5 rounded-lg text-xs font-semibold border
                    ${user?.kycStatus === 'PENDING'
                      ? 'bg-solar-50 text-solar-700 border-solar-200'
                      : 'bg-red-50 text-red-700 border-red-200'
                    }
                  `}>
                    KYC: {user?.kycStatus}
                  </div>
                )}

                {/* User Dropdown */}
                <div className="relative" ref={dropdownRef}>
                  <button
                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}
                    className="flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors"
                  >
                    <div className="w-8 h-8 bg-gradient-to-br from-solar-500 to-eco-500 rounded-lg flex items-center justify-center">
                      <span className="text-white font-semibold text-sm">
                        {user?.email.charAt(0).toUpperCase()}
                      </span>
                    </div>
                    <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${userDropdownOpen ? 'rotate-180' : ''}`} />
                  </button>

                  {/* Dropdown Menu */}
                  {userDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50">
                      {/* User Info */}
                      <div className="px-4 py-3 border-b border-gray-100">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-gradient-to-br from-solar-500 to-eco-500 rounded-lg flex items-center justify-center">
                            <span className="text-white font-bold">
                              {user?.email.charAt(0).toUpperCase()}
                            </span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-semibold text-gray-900 truncate">
                              {user?.email.split('@')[0]}
                            </p>
                            <p className="text-xs text-gray-600">
                              ID: {user?.referralId}
                            </p>
                          </div>
                        </div>
                      </div>

                      {/* Menu Items */}
                      <div className="py-1">
                        <button
                          onClick={() => {
                            setUserDropdownOpen(false);
                            // Add profile navigation if needed
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        >
                          <User className="h-4 w-4" />
                          <span>Profile Settings</span>
                        </button>
                        <button
                          onClick={() => {
                            setUserDropdownOpen(false);
                            onTabChange('wallet');
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors"
                        >
                          <CreditCard className="h-4 w-4" />
                          <span>Billing & Payments</span>
                        </button>
                        <div className="border-t border-gray-100 my-1"></div>
                        <button
                          onClick={() => {
                            setUserDropdownOpen(false);
                            handleLogout();
                          }}
                          className="w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
                        >
                          <LogOut className="h-4 w-4" />
                          <span>Sign Out</span>
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </Flex>
          </div>
        </header>

        {/* KYC Notification Banner */}
        {user?.kycStatus !== 'APPROVED' && !kycBannerDismissed && (
          <div className="bg-gradient-to-r from-solar-50 to-eco-50 border-b border-solar-200">
            <div className="px-4 sm:px-6 lg:px-8 xl:px-12">
              <div className="flex items-center justify-between py-3">
                <div className="flex items-center space-x-3">
                  <AlertCircle className="h-5 w-5 text-solar-600" />
                  <div>
                    <p className="text-sm font-medium text-solar-800">
                      {user?.kycStatus === 'PENDING'
                        ? 'KYC verification in progress'
                        : 'Complete your KYC verification'
                      }
                    </p>
                    <p className="text-xs text-solar-600">
                      {user?.kycStatus === 'PENDING'
                        ? 'Your documents are being reviewed. This usually takes 1-3 business days.'
                        : 'Verify your identity to enable withdrawals and unlock all features.'
                      }
                    </p>
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  {user?.kycStatus !== 'PENDING' && (
                    <Button
                      size="sm"
                      onClick={() => onTabChange('kyc')}
                      className="bg-solar-600 hover:bg-solar-700 text-white"
                    >
                      Complete KYC
                    </Button>
                  )}
                  <button
                    onClick={() => setKycBannerDismissed(true)}
                    className="text-solar-500 hover:text-solar-700 p-1"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Main Content */}
        <main className="flex-1 bg-gray-50 overflow-y-auto">
          <div className="px-4 sm:px-6 lg:px-8 xl:px-12 py-6">
            <div className="max-w-7xl mx-auto">
              {children}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
};
