{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\nexport function formatCurrency(amount: number, currency = 'USD'): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency,\n    minimumFractionDigits: 2,\n    maximumFractionDigits: 2,\n  }).format(amount);\n}\n\nexport function formatNumber(num: number, decimals = 2): string {\n  return new Intl.NumberFormat('en-US', {\n    minimumFractionDigits: decimals,\n    maximumFractionDigits: decimals,\n  }).format(num);\n}\n\nexport function formatDate(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  }).format(d);\n}\n\nexport function formatDateTime(date: Date | string): string {\n  const d = typeof date === 'string' ? new Date(date) : date;\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(d);\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text;\n  return text.slice(0, maxLength) + '...';\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9);\n}\n\nexport function sleep(ms: number): Promise<void> {\n  return new Promise(resolve => setTimeout(resolve, ms));\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean;\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n\nexport function copyToClipboard(text: string): Promise<void> {\n  if (navigator.clipboard) {\n    return navigator.clipboard.writeText(text);\n  }\n  \n  // Fallback for older browsers\n  const textArea = document.createElement('textarea');\n  textArea.value = text;\n  document.body.appendChild(textArea);\n  textArea.focus();\n  textArea.select();\n  \n  try {\n    document.execCommand('copy');\n    return Promise.resolve();\n  } catch (err) {\n    return Promise.reject(err);\n  } finally {\n    document.body.removeChild(textArea);\n  }\n}\n\nexport function validateEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function validatePassword(password: string): {\n  isValid: boolean;\n  errors: string[];\n} {\n  const errors: string[] = [];\n  \n  if (password.length < 8) {\n    errors.push('Password must be at least 8 characters long');\n  }\n  \n  if (!/[A-Z]/.test(password)) {\n    errors.push('Password must contain at least one uppercase letter');\n  }\n  \n  if (!/[a-z]/.test(password)) {\n    errors.push('Password must contain at least one lowercase letter');\n  }\n  \n  if (!/\\d/.test(password)) {\n    errors.push('Password must contain at least one number');\n  }\n  \n  if (!/[!@#$%^&*(),.?\":{}|<>]/.test(password)) {\n    errors.push('Password must contain at least one special character');\n  }\n  \n  return {\n    isValid: errors.length === 0,\n    errors,\n  };\n}\n\nexport function calculateROI(\n  investment: number,\n  dailyRate: number,\n  days: number\n): number {\n  return investment * (dailyRate / 100) * days;\n}\n\nexport function calculateTHSPrice(ths: number, pricePerTHS: number): number {\n  return ths * pricePerTHS;\n}\n\nexport function formatTHS(ths: number): string {\n  if (ths >= 1000) {\n    return `${(ths / 1000).toFixed(1)}K TH/s`;\n  }\n  return `${ths.toFixed(2)} TH/s`;\n}\n\nexport function getTimeUntilNextPayout(): {\n  days: number;\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextSaturday = new Date();\n  \n  // Set to next Saturday at 15:00 UTC\n  nextSaturday.setUTCDate(now.getUTCDate() + (6 - now.getUTCDay()));\n  nextSaturday.setUTCHours(15, 0, 0, 0);\n  \n  // If it's already past Saturday 15:00, move to next week\n  if (now > nextSaturday) {\n    nextSaturday.setUTCDate(nextSaturday.getUTCDate() + 7);\n  }\n  \n  const diff = nextSaturday.getTime() - now.getTime();\n  \n  const days = Math.floor(diff / (1000 * 60 * 60 * 24));\n  const hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { days, hours, minutes, seconds };\n}\n\nexport function getTimeUntilBinaryPayout(): {\n  hours: number;\n  minutes: number;\n  seconds: number;\n} {\n  const now = new Date();\n  const nextMidnight = new Date();\n  \n  // Set to next midnight UTC\n  nextMidnight.setUTCDate(now.getUTCDate() + 1);\n  nextMidnight.setUTCHours(0, 0, 0, 0);\n  \n  const diff = nextMidnight.getTime() - now.getTime();\n  \n  const hours = Math.floor(diff / (1000 * 60 * 60));\n  const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));\n  const seconds = Math.floor((diff % (1000 * 60)) / 1000);\n  \n  return { hours, minutes, seconds };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,eAAe,MAAc,EAAE,WAAW,KAAK;IAC7D,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;QACA,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,GAAW,EAAE,WAAW,CAAC;IACpD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,eAAe,IAAmB;IAChD,MAAM,IAAI,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IACtD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,MAAM,EAAU;IAC9B,OAAO,IAAI,QAAQ,CAAA,UAAW,WAAW,SAAS;AACpD;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF;AAEO,SAAS,gBAAgB,IAAY;IAC1C,IAAI,UAAU,SAAS,EAAE;QACvB,OAAO,UAAU,SAAS,CAAC,SAAS,CAAC;IACvC;IAEA,8BAA8B;IAC9B,MAAM,WAAW,SAAS,aAAa,CAAC;IACxC,SAAS,KAAK,GAAG;IACjB,SAAS,IAAI,CAAC,WAAW,CAAC;IAC1B,SAAS,KAAK;IACd,SAAS,MAAM;IAEf,IAAI;QACF,SAAS,WAAW,CAAC;QACrB,OAAO,QAAQ,OAAO;IACxB,EAAE,OAAO,KAAK;QACZ,OAAO,QAAQ,MAAM,CAAC;IACxB,SAAU;QACR,SAAS,IAAI,CAAC,WAAW,CAAC;IAC5B;AACF;AAEO,SAAS,cAAc,KAAa;IACzC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,iBAAiB,QAAgB;IAI/C,MAAM,SAAmB,EAAE;IAE3B,IAAI,SAAS,MAAM,GAAG,GAAG;QACvB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,QAAQ,IAAI,CAAC,WAAW;QAC3B,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,KAAK,IAAI,CAAC,WAAW;QACxB,OAAO,IAAI,CAAC;IACd;IAEA,IAAI,CAAC,yBAAyB,IAAI,CAAC,WAAW;QAC5C,OAAO,IAAI,CAAC;IACd;IAEA,OAAO;QACL,SAAS,OAAO,MAAM,KAAK;QAC3B;IACF;AACF;AAEO,SAAS,aACd,UAAkB,EAClB,SAAiB,EACjB,IAAY;IAEZ,OAAO,aAAa,CAAC,YAAY,GAAG,IAAI;AAC1C;AAEO,SAAS,kBAAkB,GAAW,EAAE,WAAmB;IAChE,OAAO,MAAM;AACf;AAEO,SAAS,UAAU,GAAW;IACnC,IAAI,OAAO,MAAM;QACf,OAAO,GAAG,CAAC,MAAM,IAAI,EAAE,OAAO,CAAC,GAAG,MAAM,CAAC;IAC3C;IACA,OAAO,GAAG,IAAI,OAAO,CAAC,GAAG,KAAK,CAAC;AACjC;AAEO,SAAS;IAMd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,oCAAoC;IACpC,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK,CAAC,IAAI,IAAI,SAAS,EAAE;IAC/D,aAAa,WAAW,CAAC,IAAI,GAAG,GAAG;IAEnC,yDAAyD;IACzD,IAAI,MAAM,cAAc;QACtB,aAAa,UAAU,CAAC,aAAa,UAAU,KAAK;IACtD;IAEA,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,OAAO,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE;IACnD,MAAM,QAAQ,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,KAAK,EAAE,IAAK,CAAC,OAAO,KAAK,EAAE;IACzE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAM;QAAO;QAAS;IAAQ;AACzC;AAEO,SAAS;IAKd,MAAM,MAAM,IAAI;IAChB,MAAM,eAAe,IAAI;IAEzB,2BAA2B;IAC3B,aAAa,UAAU,CAAC,IAAI,UAAU,KAAK;IAC3C,aAAa,WAAW,CAAC,GAAG,GAAG,GAAG;IAElC,MAAM,OAAO,aAAa,OAAO,KAAK,IAAI,OAAO;IAEjD,MAAM,QAAQ,KAAK,KAAK,CAAC,OAAO,CAAC,OAAO,KAAK,EAAE;IAC/C,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,KAAK,EAAE,IAAK,CAAC,OAAO,EAAE;IACjE,MAAM,UAAU,KAAK,KAAK,CAAC,AAAC,OAAO,CAAC,OAAO,EAAE,IAAK;IAElD,OAAO;QAAE;QAAO;QAAS;IAAQ;AACnC", "debugId": null}}, {"offset": {"line": 197, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Container.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface ContainerProps {\n  children: React.ReactNode;\n  className?: string;\n  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full';\n}\n\nconst Container: React.FC<ContainerProps> = ({ \n  children, \n  className, \n  size = 'lg' \n}) => {\n  const sizeClasses = {\n    sm: 'max-w-2xl',\n    md: 'max-w-4xl',\n    lg: 'max-w-6xl',\n    xl: 'max-w-7xl',\n    full: 'max-w-full',\n  };\n\n  return (\n    <div\n      className={cn(\n        'mx-auto px-4 sm:px-6 lg:px-8',\n        sizeClasses[size],\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Container };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAWA,MAAM,YAAsC,CAAC,EAC3C,QAAQ,EACR,SAAS,EACT,OAAO,IAAI,EACZ;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,WAAW,CAAC,KAAK,EACjB;kBAGD;;;;;;AAGP;KAxBM", "debugId": null}}, {"offset": {"line": 235, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Grid.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface GridProps {\n  children: React.ReactNode;\n  className?: string;\n  cols?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n  gap?: number;\n}\n\nconst Grid: React.FC<GridProps> = ({ \n  children, \n  className, \n  cols = { default: 1, md: 2, lg: 3 },\n  gap = 6\n}) => {\n  const getGridClasses = () => {\n    const classes = ['grid'];\n    \n    // Default columns\n    if (cols.default) {\n      classes.push(`grid-cols-${cols.default}`);\n    }\n    \n    // Responsive columns\n    if (cols.sm) {\n      classes.push(`sm:grid-cols-${cols.sm}`);\n    }\n    if (cols.md) {\n      classes.push(`md:grid-cols-${cols.md}`);\n    }\n    if (cols.lg) {\n      classes.push(`lg:grid-cols-${cols.lg}`);\n    }\n    if (cols.xl) {\n      classes.push(`xl:grid-cols-${cols.xl}`);\n    }\n    \n    // Gap\n    classes.push(`gap-${gap}`);\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getGridClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\ninterface GridItemProps {\n  children: React.ReactNode;\n  className?: string;\n  span?: {\n    default?: number;\n    sm?: number;\n    md?: number;\n    lg?: number;\n    xl?: number;\n  };\n}\n\nconst GridItem: React.FC<GridItemProps> = ({ \n  children, \n  className, \n  span \n}) => {\n  const getSpanClasses = () => {\n    if (!span) return '';\n    \n    const classes = [];\n    \n    if (span.default) {\n      classes.push(`col-span-${span.default}`);\n    }\n    if (span.sm) {\n      classes.push(`sm:col-span-${span.sm}`);\n    }\n    if (span.md) {\n      classes.push(`md:col-span-${span.md}`);\n    }\n    if (span.lg) {\n      classes.push(`lg:col-span-${span.lg}`);\n    }\n    if (span.xl) {\n      classes.push(`xl:col-span-${span.xl}`);\n    }\n    \n    return classes.join(' ');\n  };\n\n  return (\n    <div className={cn(getSpanClasses(), className)}>\n      {children}\n    </div>\n  );\n};\n\nexport { Grid, GridItem };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAkBA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,OAAO;IAAE,SAAS;IAAG,IAAI;IAAG,IAAI;AAAE,CAAC,EACnC,MAAM,CAAC,EACR;IACC,MAAM,iBAAiB;QACrB,MAAM,UAAU;YAAC;SAAO;QAExB,kBAAkB;QAClB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,UAAU,EAAE,KAAK,OAAO,EAAE;QAC1C;QAEA,qBAAqB;QACrB,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,aAAa,EAAE,KAAK,EAAE,EAAE;QACxC;QAEA,MAAM;QACN,QAAQ,IAAI,CAAC,CAAC,IAAI,EAAE,KAAK;QAEzB,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP;KAvCM;AAqDN,MAAM,WAAoC,CAAC,EACzC,QAAQ,EACR,SAAS,EACT,IAAI,EACL;IACC,MAAM,iBAAiB;QACrB,IAAI,CAAC,MAAM,OAAO;QAElB,MAAM,UAAU,EAAE;QAElB,IAAI,KAAK,OAAO,EAAE;YAChB,QAAQ,IAAI,CAAC,CAAC,SAAS,EAAE,KAAK,OAAO,EAAE;QACzC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QACA,IAAI,KAAK,EAAE,EAAE;YACX,QAAQ,IAAI,CAAC,CAAC,YAAY,EAAE,KAAK,EAAE,EAAE;QACvC;QAEA,OAAO,QAAQ,IAAI,CAAC;IACtB;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,kBAAkB;kBAClC;;;;;;AAGP;MAlCM", "debugId": null}}, {"offset": {"line": 328, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/Flex.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\ninterface FlexProps {\n  children: React.ReactNode;\n  className?: string;\n  direction?: 'row' | 'col' | 'row-reverse' | 'col-reverse';\n  align?: 'start' | 'center' | 'end' | 'stretch' | 'baseline';\n  justify?: 'start' | 'center' | 'end' | 'between' | 'around' | 'evenly';\n  wrap?: 'wrap' | 'nowrap' | 'wrap-reverse';\n  gap?: number;\n}\n\nconst Flex: React.FC<FlexProps> = ({\n  children,\n  className,\n  direction = 'row',\n  align = 'start',\n  justify = 'start',\n  wrap = 'nowrap',\n  gap = 0,\n}) => {\n  const directionClasses = {\n    row: 'flex-row',\n    col: 'flex-col',\n    'row-reverse': 'flex-row-reverse',\n    'col-reverse': 'flex-col-reverse',\n  };\n\n  const alignClasses = {\n    start: 'items-start',\n    center: 'items-center',\n    end: 'items-end',\n    stretch: 'items-stretch',\n    baseline: 'items-baseline',\n  };\n\n  const justifyClasses = {\n    start: 'justify-start',\n    center: 'justify-center',\n    end: 'justify-end',\n    between: 'justify-between',\n    around: 'justify-around',\n    evenly: 'justify-evenly',\n  };\n\n  const wrapClasses = {\n    wrap: 'flex-wrap',\n    nowrap: 'flex-nowrap',\n    'wrap-reverse': 'flex-wrap-reverse',\n  };\n\n  return (\n    <div\n      className={cn(\n        'flex',\n        directionClasses[direction],\n        alignClasses[align],\n        justifyClasses[justify],\n        wrapClasses[wrap],\n        gap > 0 && `gap-${gap}`,\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Flex };\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAeA,MAAM,OAA4B,CAAC,EACjC,QAAQ,EACR,SAAS,EACT,YAAY,KAAK,EACjB,QAAQ,OAAO,EACf,UAAU,OAAO,EACjB,OAAO,QAAQ,EACf,MAAM,CAAC,EACR;IACC,MAAM,mBAAmB;QACvB,KAAK;QACL,KAAK;QACL,eAAe;QACf,eAAe;IACjB;IAEA,MAAM,eAAe;QACnB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,UAAU;IACZ;IAEA,MAAM,iBAAiB;QACrB,OAAO;QACP,QAAQ;QACR,KAAK;QACL,SAAS;QACT,QAAQ;QACR,QAAQ;IACV;IAEA,MAAM,cAAc;QAClB,MAAM;QACN,QAAQ;QACR,gBAAgB;IAClB;IAEA,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QACA,gBAAgB,CAAC,UAAU,EAC3B,YAAY,CAAC,MAAM,EACnB,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,MAAM,KAAK,CAAC,IAAI,EAAE,KAAK,EACvB;kBAGD;;;;;;AAGP;KAtDM", "debugId": null}}, {"offset": {"line": 385, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/layout/index.ts"], "sourcesContent": ["export { Container } from './Container';\nexport { Grid, GridItem } from './Grid';\nexport { Flex } from './Flex';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Button.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cva, type VariantProps } from 'class-variance-authority';\nimport { cn } from '@/lib/utils';\n\nconst buttonVariants = cva(\n  'inline-flex items-center justify-center rounded-xl font-semibold transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl',\n  {\n    variants: {\n      variant: {\n        primary: 'bg-gradient-to-r from-solar-500 to-solar-600 text-white hover:from-solar-600 hover:to-solar-700 focus:ring-solar-500 animate-pulse-glow',\n        secondary: 'bg-gradient-to-r from-gray-100 to-gray-200 text-gray-800 hover:from-gray-200 hover:to-gray-300 focus:ring-gray-500',\n        success: 'bg-gradient-to-r from-eco-500 to-eco-600 text-white hover:from-eco-600 hover:to-eco-700 focus:ring-eco-500',\n        danger: 'bg-gradient-to-r from-red-500 to-red-600 text-white hover:from-red-600 hover:to-red-700 focus:ring-red-500',\n        outline: 'border-2 border-solar-500 bg-transparent text-solar-600 hover:bg-solar-500 hover:text-white focus:ring-solar-500 backdrop-blur-sm',\n        ghost: 'text-gray-600 hover:bg-solar-50 hover:text-solar-700 focus:ring-solar-500 rounded-lg',\n        link: 'text-solar-600 underline-offset-4 hover:underline focus:ring-solar-500 hover:text-solar-700',\n        premium: 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 focus:ring-purple-500',\n        glass: 'glass-morphism text-dark-900 hover:bg-white/20 backdrop-blur-xl border border-white/20',\n      },\n      size: {\n        sm: 'h-10 px-4 text-sm rounded-lg',\n        md: 'h-12 px-6 text-base rounded-xl',\n        lg: 'h-14 px-8 text-lg rounded-xl',\n        xl: 'h-16 px-10 text-xl rounded-2xl font-bold',\n        icon: 'h-12 w-12 rounded-xl',\n      },\n    },\n    defaultVariants: {\n      variant: 'primary',\n      size: 'md',\n    },\n  }\n);\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  loading?: boolean;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, loading, leftIcon, rightIcon, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <div className=\"mr-2\">\n            <div className=\"spinner\" />\n          </div>\n        )}\n        {leftIcon && !loading && <span className=\"mr-2\">{leftIcon}</span>}\n        {children}\n        {rightIcon && !loading && <span className=\"ml-2\">{rightIcon}</span>}\n      </button>\n    );\n  }\n);\n\nButton.displayName = 'Button';\n\nexport { Button, buttonVariants };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AAJA;;;;;AAMA,MAAM,iBAAiB,CAAA,GAAA,mKAAA,CAAA,MAAG,AAAD,EACvB,yQACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,WAAW;YACX,SAAS;YACT,QAAQ;YACR,SAAS;YACT,OAAO;YACP,MAAM;YACN,SAAS;YACT,OAAO;QACT;QACA,MAAM;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAWF,MAAM,uBAAS,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ,EAAE,SAAS,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACzF,qBACE,6LAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;YAGlB,YAAY,CAAC,yBAAW,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;YAChD;YACA,aAAa,CAAC,yBAAW,6LAAC;gBAAK,WAAU;0BAAQ;;;;;;;;;;;;AAGxD;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 515, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Card.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn(\n          'rounded-2xl border border-gray-200/50 bg-white/90 backdrop-blur-xl shadow-xl hover:shadow-2xl transition-all duration-300 transform hover:scale-[1.02] overflow-hidden',\n          className\n        )}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCard.displayName = 'Card';\n\nexport interface CardHeaderProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardHeader = React.forwardRef<HTMLDivElement, CardHeaderProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardHeader.displayName = 'CardHeader';\n\nexport interface CardTitleProps extends React.HTMLAttributes<HTMLHeadingElement> {\n  children: React.ReactNode;\n}\n\nconst CardTitle = React.forwardRef<HTMLParagraphElement, CardTitleProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <h3\n        ref={ref}\n        className={cn('text-xl font-semibold leading-none tracking-tight text-dark-900', className)}\n        {...props}\n      >\n        {children}\n      </h3>\n    );\n  }\n);\n\nCardTitle.displayName = 'CardTitle';\n\nexport interface CardDescriptionProps extends React.HTMLAttributes<HTMLParagraphElement> {\n  children: React.ReactNode;\n}\n\nconst CardDescription = React.forwardRef<HTMLParagraphElement, CardDescriptionProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <p\n        ref={ref}\n        className={cn('text-sm text-gray-500', className)}\n        {...props}\n      >\n        {children}\n      </p>\n    );\n  }\n);\n\nCardDescription.displayName = 'CardDescription';\n\nexport interface CardContentProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardContent = React.forwardRef<HTMLDivElement, CardContentProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardContent.displayName = 'CardContent';\n\nexport interface CardFooterProps extends React.HTMLAttributes<HTMLDivElement> {\n  children: React.ReactNode;\n}\n\nconst CardFooter = React.forwardRef<HTMLDivElement, CardFooterProps>(\n  ({ className, children, ...props }, ref) => {\n    return (\n      <div\n        ref={ref}\n        className={cn('flex items-center p-6 pt-0', className)}\n        {...props}\n      >\n        {children}\n      </div>\n    );\n  }\n);\n\nCardFooter.displayName = 'CardFooter';\n\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AAHA;;;;AASA,MAAM,qBAAO,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0KACA;QAED,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAMnB,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,OACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG;AAMzB,MAAM,0BAAY,6JAAA,CAAA,UAAK,CAAC,UAAU,OAChC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,mEAAmE;QAChF,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,UAAU,WAAW,GAAG;AAMxB,MAAM,gCAAkB,6JAAA,CAAA,UAAK,CAAC,UAAU,OACtC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QACtC,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,gBAAgB,WAAW,GAAG;AAM9B,MAAM,4BAAc,6JAAA,CAAA,UAAK,CAAC,UAAU,OAClC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QACzB,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,YAAY,WAAW,GAAG;AAM1B,MAAM,2BAAa,6JAAA,CAAA,UAAK,CAAC,UAAU,QACjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAClC,qBACE,6LAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;kBAER;;;;;;AAGP;;AAGF,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Input.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  ({ className, type, label, error, leftIcon, rightIcon, ...props }, ref) => {\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-dark-700 mb-2\">\n            {label}\n          </label>\n        )}\n        <div className=\"relative\">\n          {leftIcon && (\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <span className=\"text-gray-400\">{leftIcon}</span>\n            </div>\n          )}\n          <input\n            type={type}\n            className={cn(\n              'flex h-14 w-full rounded-xl border-2 border-gray-200 bg-white/90 backdrop-blur-sm px-4 py-4 text-base shadow-inner focus:shadow-lg placeholder:text-gray-400 focus:outline-none focus:ring-2 focus:ring-solar-500 focus:border-solar-500 disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 hover:border-gray-300',\n              leftIcon && 'pl-12',\n              rightIcon && 'pr-12',\n              error && 'border-red-500 focus:ring-red-500 focus:border-red-500',\n              className\n            )}\n            ref={ref}\n            {...props}\n          />\n          {rightIcon && (\n            <div className=\"absolute inset-y-0 right-0 pr-3 flex items-center\">\n              <span className=\"text-gray-400\">{rightIcon}</span>\n            </div>\n          )}\n        </div>\n        {error && (\n          <p className=\"mt-1 text-sm text-red-600\">{error}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = 'Input';\n\nexport { Input };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAYA,MAAM,sBAAQ,6JAAA,CAAA,UAAK,CAAC,UAAU,MAC5B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IACjE,qBACE,6LAAC;QAAI,WAAU;;YACZ,uBACC,6LAAC;gBAAM,WAAU;0BACd;;;;;;0BAGL,6LAAC;gBAAI,WAAU;;oBACZ,0BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;kCAGrC,6LAAC;wBACC,MAAM;wBACN,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,8UACA,YAAY,SACZ,aAAa,SACb,SAAS,0DACT;wBAEF,KAAK;wBACJ,GAAG,KAAK;;;;;;oBAEV,2BACC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAiB;;;;;;;;;;;;;;;;;YAItC,uBACC,6LAAC;gBAAE,WAAU;0BAA6B;;;;;;;;;;;;AAIlD;;AAGF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 738, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Modal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useEffect } from 'react';\nimport { createPortal } from 'react-dom';\nimport { X } from 'lucide-react';\nimport { cn } from '@/lib/utils';\nimport { Button } from './Button';\n\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title: string;\n  children: React.ReactNode;\n  size?: 'sm' | 'md' | 'lg' | 'xl';\n  showCloseButton?: boolean;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  children,\n  size = 'md',\n  showCloseButton = true,\n}) => {\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = 'hidden';\n    } else {\n      document.body.style.overflow = 'unset';\n    }\n\n    return () => {\n      document.body.style.overflow = 'unset';\n    };\n  }, [isOpen]);\n\n  useEffect(() => {\n    const handleEscape = (event: KeyboardEvent) => {\n      if (event.key === 'Escape') {\n        onClose();\n      }\n    };\n\n    if (isOpen) {\n      document.addEventListener('keydown', handleEscape);\n    }\n\n    return () => {\n      document.removeEventListener('keydown', handleEscape);\n    };\n  }, [isOpen, onClose]);\n\n  if (!isOpen) return null;\n\n  const sizeClasses = {\n    sm: 'max-w-md',\n    md: 'max-w-lg',\n    lg: 'max-w-2xl',\n    xl: 'max-w-4xl',\n  };\n\n  const modalContent = (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center p-4\">\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black bg-opacity-50 transition-opacity\"\n        onClick={onClose}\n      />\n      \n      {/* Modal */}\n      <div\n        className={cn(\n          'relative w-full bg-white rounded-xl shadow-xl transform transition-all',\n          sizeClasses[size]\n        )}\n        onClick={(e) => e.stopPropagation()}\n      >\n        {/* Header */}\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n          <h2 className=\"text-xl font-semibold text-dark-900\">{title}</h2>\n          {showCloseButton && (\n            <Button\n              variant=\"ghost\"\n              size=\"icon\"\n              onClick={onClose}\n              className=\"h-8 w-8 rounded-full\"\n            >\n              <X className=\"h-4 w-4\" />\n            </Button>\n          )}\n        </div>\n        \n        {/* Content */}\n        <div className=\"p-6\">\n          {children}\n        </div>\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\nexport { Modal };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;;;AANA;;;;;;AAiBA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,QAAQ,EACR,OAAO,IAAI,EACX,kBAAkB,IAAI,EACvB;;IACC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;2BAAE;YACR,MAAM;gDAAe,CAAC;oBACpB,IAAI,MAAM,GAAG,KAAK,UAAU;wBAC1B;oBACF;gBACF;;YAEA,IAAI,QAAQ;gBACV,SAAS,gBAAgB,CAAC,WAAW;YACvC;YAEA;mCAAO;oBACL,SAAS,mBAAmB,CAAC,WAAW;gBAC1C;;QACF;0BAAG;QAAC;QAAQ;KAAQ;IAEpB,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,6BACJ,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBACC,WAAU;gBACV,SAAS;;;;;;0BAIX,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,0EACA,WAAW,CAAC,KAAK;gBAEnB,SAAS,CAAC,IAAM,EAAE,eAAe;;kCAGjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAAuC;;;;;;4BACpD,iCACC,6LAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;gCACT,WAAU;0CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oCAAC,WAAU;;;;;;;;;;;;;;;;;kCAMnB,6LAAC;wBAAI,WAAU;kCACZ;;;;;;;;;;;;;;;;;;IAMT,qBAAO,CAAA,GAAA,oKAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AACjD;GArFM;KAAA", "debugId": null}}, {"offset": {"line": 887, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\nimport { cn } from '@/lib/utils';\n\nexport interface LoadingProps {\n  size?: 'sm' | 'md' | 'lg';\n  className?: string;\n  text?: string;\n}\n\nconst Loading: React.FC<LoadingProps> = ({ size = 'md', className, text }) => {\n  const sizeClasses = {\n    sm: 'h-4 w-4',\n    md: 'h-8 w-8',\n    lg: 'h-12 w-12',\n  };\n\n  return (\n    <div className={cn('flex flex-col items-center justify-center', className)}>\n      <div\n        className={cn(\n          'animate-spin rounded-full border-2 border-gray-300 border-t-solar-500',\n          sizeClasses[size]\n        )}\n      />\n      {text && (\n        <p className=\"mt-2 text-sm text-gray-600\">{text}</p>\n      )}\n    </div>\n  );\n};\n\nexport interface LoadingOverlayProps {\n  isLoading: boolean;\n  text?: string;\n  children: React.ReactNode;\n}\n\nconst LoadingOverlay: React.FC<LoadingOverlayProps> = ({\n  isLoading,\n  text = 'Loading...',\n  children,\n}) => {\n  return (\n    <div className=\"relative\">\n      {children}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10\">\n          <Loading text={text} />\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport { Loading, LoadingOverlay };\n"], "names": [], "mappings": ";;;;;AAGA;AAHA;;;AAWA,MAAM,UAAkC,CAAC,EAAE,OAAO,IAAI,EAAE,SAAS,EAAE,IAAI,EAAE;IACvE,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,6LAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;;0BAC9D,6LAAC;gBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yEACA,WAAW,CAAC,KAAK;;;;;;YAGpB,sBACC,6LAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;KApBM;AA4BN,MAAM,iBAAgD,CAAC,EACrD,SAAS,EACT,OAAO,YAAY,EACnB,QAAQ,EACT;IACC,qBACE,6LAAC;QAAI,WAAU;;YACZ;YACA,2BACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAQ,MAAM;;;;;;;;;;;;;;;;;AAKzB;MAfM", "debugId": null}}, {"offset": {"line": 968, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/ui/index.ts"], "sourcesContent": ["export { Button, buttonVariants, type ButtonProps } from './Button';\nexport { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from './Card';\nexport { Input, type InputProps } from './Input';\nexport { Modal, type ModalProps } from './Modal';\nexport { Loading, LoadingOverlay, type LoadingProps } from './Loading';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1001, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/SolarPanel.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const SolarPanel: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"4\" width=\"20\" height=\"16\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"2\" y1=\"8\" x2=\"22\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"12\" x2=\"22\" y2=\"12\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"2\" y1=\"16\" x2=\"22\" y2=\"16\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"6\" y1=\"4\" x2=\"6\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"4\" x2=\"10\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"4\" x2=\"14\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"4\" x2=\"18\" y2=\"20\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <circle cx=\"20\" cy=\"2\" r=\"1\" fill=\"currentColor\"/>\n      <path d=\"M19 1l1 1-1 1-1-1z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,aAAkC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACtE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACrE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAI,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACrE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACvE,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,6LAAC;gBAAK,GAAE;gBAAqB,MAAK;;;;;;;;;;;;AAGxC;KAtBa", "debugId": null}}, {"offset": {"line": 1151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/MiningRig.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const MiningRig: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <rect x=\"2\" y=\"6\" width=\"20\" height=\"12\" rx=\"2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <rect x=\"4\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"8\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"4\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"8\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"12\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <rect x=\"16\" y=\"12\" width=\"3\" height=\"3\" rx=\"0.5\" fill=\"currentColor\"/>\n      <circle cx=\"20\" cy=\"4\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"20\" y1=\"4\" x2=\"20\" y2=\"6\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"18\" y1=\"2\" x2=\"22\" y2=\"2\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"19\" y1=\"1\" x2=\"21\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n      <line x1=\"21\" y1=\"1\" x2=\"19\" y2=\"3\" stroke=\"currentColor\" strokeWidth=\"1\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;AAAA;;AASO,MAAM,YAAiC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACrE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAK,QAAO;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACrD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAI,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAI,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACtD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,6LAAC;gBAAK,GAAE;gBAAK,GAAE;gBAAK,OAAM;gBAAI,QAAO;gBAAI,IAAG;gBAAM,MAAK;;;;;;0BACvD,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAI,GAAE;gBAAI,MAAK;;;;;;0BAClC,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG5E;KA1Ba", "debugId": null}}, {"offset": {"line": 1353, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/Cryptocurrency.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Cryptocurrency: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h8M12 8v8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M10 8h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M10 14h4c1.1 0 2 .9 2 2s-.9 2-2 2h-4\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"12\" y1=\"6\" x2=\"12\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"12\" y1=\"16\" x2=\"12\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n\nexport const Bitcoin: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <circle cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 12h4c1.1 0 2-.9 2-2s-.9-2-2-2H8v8h4c1.1 0 2-.9 2-2s-.9-2-2-2\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <line x1=\"10\" y1=\"6\" x2=\"10\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"10\" y1=\"16\" x2=\"10\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"6\" x2=\"14\" y2=\"8\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <line x1=\"14\" y1=\"16\" x2=\"14\" y2=\"18\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;AAAA;;AASO,MAAM,iBAAsC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAC1E,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,6LAAC;gBAAK,GAAE;gBAAiB,QAAO;gBAAe,aAAY;;;;;;0BAC3D,6LAAC;gBAAK,GAAE;gBAAsC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACzF,6LAAC;gBAAK,GAAE;gBAAuC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1F,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;KAlBa;AAoBN,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAK,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1E,6LAAC;gBAAK,GAAE;gBAAkE,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrH,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAI,IAAG;gBAAK,IAAG;gBAAI,QAAO;gBAAe,aAAY;;;;;;0BACtE,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;MAlBa", "debugId": null}}, {"offset": {"line": 1541, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/EcoFriendly.tsx"], "sourcesContent": ["'use client';\n\nimport React from 'react';\n\ninterface IconProps {\n  className?: string;\n  size?: number;\n}\n\nexport const Leaf: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M12 2C8 2 5 5 5 9c0 5 7 13 7 13s7-8 7-13c0-4-3-7-7-7z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 9c0-2-1-3-3-3s-3 1-3 3 1 3 3 3 3-1 3-3z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8 9c0 1 1 2 2 2s2-1 2-2-1-2-2-2-2 1-2 2z\" fill=\"currentColor\"/>\n    </svg>\n  );\n};\n\nexport const Recycle: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <path d=\"M7 19H4.815a1.83 1.83 0 01-1.57-.881 1.785 1.785 0 01-.004-1.784L7.196 9.5\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M11 19h8.203a1.83 1.83 0 001.556-.89 1.784 1.784 0 000-1.775l-1.226-2.12\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M14 16l-3 3 3 3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M8.293 13.596L7.196 9.5l3.1 1.598\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M9.344 5.811L11.271 2a1.784 1.784 0 011.57-.881c.65 0 1.235.361 1.556.881l3.68 6.361\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M16 8l3-3-3-3\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n    </svg>\n  );\n};\n\nexport const WindTurbine: React.FC<IconProps> = ({ className, size = 24 }) => {\n  return (\n    <svg\n      width={size}\n      height={size}\n      viewBox=\"0 0 24 24\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      className={className}\n    >\n      <line x1=\"12\" y1=\"12\" x2=\"12\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n      <path d=\"M12 12L8 4c-1-2 0-4 2-4s3 2 2 4l-2 8z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l8-4c2-1 4 0 4 2s-2 3-4 2l-8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <path d=\"M12 12l-4 8c-1 2-3 2-4 0s0-3 2-4l8-2z\" stroke=\"currentColor\" strokeWidth=\"2\" fill=\"none\"/>\n      <circle cx=\"12\" cy=\"12\" r=\"1\" fill=\"currentColor\"/>\n      <line x1=\"10\" y1=\"22\" x2=\"14\" y2=\"22\" stroke=\"currentColor\" strokeWidth=\"2\"/>\n    </svg>\n  );\n};\n"], "names": [], "mappings": ";;;;;;AAAA;;AASO,MAAM,OAA4B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IAChE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAAwD,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3G,6LAAC;gBAAK,GAAE;gBAA8C,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACjG,6LAAC;gBAAK,GAAE;gBAA4C,MAAK;;;;;;;;;;;;AAG/D;KAfa;AAiBN,MAAM,UAA+B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACnE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,GAAE;gBAA6E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAChI,6LAAC;gBAAK,GAAE;gBAA2E,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC9H,6LAAC;gBAAK,GAAE;gBAAkB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACrE,6LAAC;gBAAK,GAAE;gBAAoC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BACvF,6LAAC;gBAAK,GAAE;gBAAuF,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC1I,6LAAC;gBAAK,GAAE;gBAAgB,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;;;;;;;AAGzE;MAlBa;AAoBN,MAAM,cAAmC,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,EAAE;IACvE,qBACE,6LAAC;QACC,OAAO;QACP,QAAQ;QACR,SAAQ;QACR,MAAK;QACL,OAAM;QACN,WAAW;;0BAEX,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;0BACxE,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAK,GAAE;gBAAwC,QAAO;gBAAe,aAAY;gBAAI,MAAK;;;;;;0BAC3F,6LAAC;gBAAO,IAAG;gBAAK,IAAG;gBAAK,GAAE;gBAAI,MAAK;;;;;;0BACnC,6LAAC;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,IAAG;gBAAK,QAAO;gBAAe,aAAY;;;;;;;;;;;;AAG9E;MAlBa", "debugId": null}}, {"offset": {"line": 1765, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/icons/index.ts"], "sourcesContent": ["export { SolarPanel } from './SolarPanel';\nexport { MiningRig } from './MiningRig';\nexport { Cryptocurrency, Bitcoin } from './Cryptocurrency';\nexport { Leaf, Recycle, WindTurbine } from './EcoFriendly';\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 1795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/DashboardLayout.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useRef, useEffect } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/hooks/useAuth';\nimport { Container, Flex } from '@/components/layout';\nimport { Button } from '@/components/ui';\nimport { SolarPanel } from '@/components/icons';\nimport {\n  LayoutDashboard,\n  Zap,\n  TrendingUp,\n  Wallet,\n  Users,\n  Shield,\n  Settings,\n  LogOut,\n  Menu,\n  X,\n  Bell,\n  AlertCircle,\n  ChevronDown,\n  User,\n  CreditCard\n} from 'lucide-react';\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n  activeTab: string;\n  onTabChange: (tab: string) => void;\n}\n\nexport const DashboardLayout: React.FC<DashboardLayoutProps> = ({\n  children,\n  activeTab,\n  onTabChange,\n}) => {\n  const { user, logout } = useAuth();\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const [kycBannerDismissed, setKycBannerDismissed] = useState(false);\n  const [userDropdownOpen, setUserDropdownOpen] = useState(false);\n  const dropdownRef = useRef<HTMLDivElement>(null);\n\n  // Base navigation items\n  const baseNavigationItems = [\n    { id: 'overview', label: 'Overview', icon: LayoutDashboard },\n    { id: 'mining', label: 'Mining Units', icon: Zap },\n    { id: 'earnings', label: 'Earnings', icon: TrendingUp },\n    { id: 'wallet', label: 'Wallet', icon: Wallet },\n    { id: 'referrals', label: 'Referrals', icon: Users },\n    { id: 'kyc', label: 'KYC Verification', icon: Shield },\n  ];\n\n  // Add admin panel for admin users\n  const navigationItems = user?.role === 'ADMIN'\n    ? [...baseNavigationItems, { id: 'admin', label: 'Admin Panel', icon: Settings }]\n    : baseNavigationItems;\n\n  const handleLogout = async () => {\n    await logout();\n  };\n\n  // Close dropdown when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {\n        setUserDropdownOpen(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      {/* Mobile sidebar overlay */}\n      {sidebarOpen && (\n        <div\n          className=\"fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden\"\n          onClick={() => setSidebarOpen(false)}\n        />\n      )}\n\n      {/* Fixed Sidebar */}\n      <aside className={`\n        fixed inset-y-0 left-0 z-50 w-64 bg-white shadow-xl border-r border-gray-200\n        transform transition-all duration-300 ease-in-out\n        ${sidebarOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n      `}>\n        <div className=\"flex flex-col h-screen\">\n          {/* Logo Header */}\n          <div className=\"flex items-center justify-between h-14 px-5 border-b border-gray-200 bg-white flex-shrink-0\">\n            <Link href=\"/\" className=\"flex items-center space-x-2\">\n              <div className=\"w-8 h-8 bg-gradient-to-br from-solar-500 to-eco-500 rounded-lg flex items-center justify-center\">\n                <SolarPanel className=\"h-5 w-5 text-white\" />\n              </div>\n              <span className=\"text-lg font-bold text-gray-900\">HashCoreX</span>\n            </Link>\n            <button\n              onClick={() => setSidebarOpen(false)}\n              className=\"lg:hidden p-1.5 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\"\n            >\n              <X className=\"h-4 w-4\" />\n            </button>\n          </div>\n\n\n\n          {/* Navigation Menu */}\n          <nav className=\"flex-1 px-3 py-4 space-y-1 min-h-0\">\n            {navigationItems.map((item) => {\n              const Icon = item.icon;\n              const isActive = activeTab === item.id;\n\n              return (\n                <button\n                  key={item.id}\n                  onClick={() => {\n                    onTabChange(item.id);\n                    setSidebarOpen(false);\n                  }}\n                  className={`\n                    w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-left transition-all duration-200 group\n                    ${isActive\n                      ? 'bg-gradient-to-r from-solar-500 to-eco-500 text-white shadow-md'\n                      : 'text-gray-700 hover:bg-gray-100 hover:text-gray-900'\n                    }\n                  `}\n                >\n                  <Icon className={`h-4 w-4 ${isActive ? 'text-white' : 'text-gray-500 group-hover:text-gray-700'}`} />\n                  <span className=\"font-medium text-sm\">{item.label}</span>\n                </button>\n              );\n            })}\n          </nav>\n\n          {/* Sidebar Footer */}\n          <div className=\"px-3 py-3 border-t border-gray-200 bg-gray-50 flex-shrink-0\">\n            <button\n              onClick={handleLogout}\n              className=\"w-full flex items-center space-x-3 px-3 py-2.5 rounded-lg text-gray-600 hover:bg-red-50 hover:text-red-600 transition-all duration-200 group\"\n            >\n              <LogOut className=\"h-4 w-4 group-hover:text-red-600\" />\n              <span className=\"font-medium text-sm\">Logout</span>\n            </button>\n          </div>\n        </div>\n      </aside>\n\n      {/* Main Content Area */}\n      <div className=\"flex-1 flex flex-col min-w-0 lg:ml-64\">\n        {/* Top Navigation Bar */}\n        <header className=\"bg-white shadow-sm border-b border-gray-200 sticky top-0 z-30\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12\">\n            <Flex justify=\"between\" align=\"center\" className=\"h-16\">\n              <div className=\"flex items-center space-x-4\">\n                <button\n                  onClick={() => setSidebarOpen(true)}\n                  className=\"lg:hidden p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\"\n                >\n                  <Menu className=\"h-6 w-6\" />\n                </button>\n                <div>\n                  <h1 className=\"text-xl font-bold text-gray-900 capitalize\">\n                    {navigationItems.find(item => item.id === activeTab)?.label || 'Dashboard'}\n                  </h1>\n                  <p className=\"text-sm text-gray-500 hidden sm:block\">\n                    Manage your mining operations and earnings\n                  </p>\n                </div>\n              </div>\n\n              <div className=\"flex items-center space-x-3\">\n                {/* Notifications */}\n                <button className=\"relative p-2 rounded-lg text-gray-500 hover:text-gray-700 hover:bg-gray-100 transition-colors\">\n                  <Bell className=\"h-5 w-5\" />\n                  <span className=\"absolute top-1 right-1 h-2 w-2 bg-red-500 rounded-full\"></span>\n                </button>\n\n                {/* KYC Status - Only show if not approved and on relevant pages */}\n                {user?.kycStatus !== 'APPROVED' && (activeTab === 'kyc' || activeTab === 'wallet') && (\n                  <div className={`\n                    px-3 py-1.5 rounded-lg text-xs font-semibold border\n                    ${user?.kycStatus === 'PENDING'\n                      ? 'bg-solar-50 text-solar-700 border-solar-200'\n                      : 'bg-red-50 text-red-700 border-red-200'\n                    }\n                  `}>\n                    KYC: {user?.kycStatus}\n                  </div>\n                )}\n\n                {/* User Dropdown */}\n                <div className=\"relative\" ref={dropdownRef}>\n                  <button\n                    onClick={() => setUserDropdownOpen(!userDropdownOpen)}\n                    className=\"flex items-center space-x-2 p-1 rounded-lg hover:bg-gray-100 transition-colors\"\n                  >\n                    <div className=\"w-8 h-8 bg-gradient-to-br from-solar-500 to-eco-500 rounded-lg flex items-center justify-center\">\n                      <span className=\"text-white font-semibold text-sm\">\n                        {user?.email.charAt(0).toUpperCase()}\n                      </span>\n                    </div>\n                    <ChevronDown className={`h-4 w-4 text-gray-500 transition-transform ${userDropdownOpen ? 'rotate-180' : ''}`} />\n                  </button>\n\n                  {/* Dropdown Menu */}\n                  {userDropdownOpen && (\n                    <div className=\"absolute right-0 mt-2 w-64 bg-white rounded-xl shadow-lg border border-gray-200 py-2 z-50\">\n                      {/* User Info */}\n                      <div className=\"px-4 py-3 border-b border-gray-100\">\n                        <div className=\"flex items-center space-x-3\">\n                          <div className=\"w-10 h-10 bg-gradient-to-br from-solar-500 to-eco-500 rounded-lg flex items-center justify-center\">\n                            <span className=\"text-white font-bold\">\n                              {user?.email.charAt(0).toUpperCase()}\n                            </span>\n                          </div>\n                          <div className=\"flex-1 min-w-0\">\n                            <p className=\"text-sm font-semibold text-gray-900 truncate\">\n                              {user?.email.split('@')[0]}\n                            </p>\n                            <p className=\"text-xs text-gray-600\">\n                              ID: {user?.referralId}\n                            </p>\n                          </div>\n                        </div>\n                      </div>\n\n                      {/* Menu Items */}\n                      <div className=\"py-1\">\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            // Add profile navigation if needed\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                        >\n                          <User className=\"h-4 w-4\" />\n                          <span>Profile Settings</span>\n                        </button>\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            onTabChange('wallet');\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors\"\n                        >\n                          <CreditCard className=\"h-4 w-4\" />\n                          <span>Billing & Payments</span>\n                        </button>\n                        <div className=\"border-t border-gray-100 my-1\"></div>\n                        <button\n                          onClick={() => {\n                            setUserDropdownOpen(false);\n                            handleLogout();\n                          }}\n                          className=\"w-full flex items-center space-x-3 px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors\"\n                        >\n                          <LogOut className=\"h-4 w-4\" />\n                          <span>Sign Out</span>\n                        </button>\n                      </div>\n                    </div>\n                  )}\n                </div>\n              </div>\n            </Flex>\n          </div>\n        </header>\n\n        {/* KYC Notification Banner */}\n        {user?.kycStatus !== 'APPROVED' && !kycBannerDismissed && (\n          <div className=\"bg-gradient-to-r from-solar-50 to-eco-50 border-b border-solar-200\">\n            <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12\">\n              <div className=\"flex items-center justify-between py-3\">\n                <div className=\"flex items-center space-x-3\">\n                  <AlertCircle className=\"h-5 w-5 text-solar-600\" />\n                  <div>\n                    <p className=\"text-sm font-medium text-solar-800\">\n                      {user?.kycStatus === 'PENDING'\n                        ? 'KYC verification in progress'\n                        : 'Complete your KYC verification'\n                      }\n                    </p>\n                    <p className=\"text-xs text-solar-600\">\n                      {user?.kycStatus === 'PENDING'\n                        ? 'Your documents are being reviewed. This usually takes 1-3 business days.'\n                        : 'Verify your identity to enable withdrawals and unlock all features.'\n                      }\n                    </p>\n                  </div>\n                </div>\n                <div className=\"flex items-center space-x-2\">\n                  {user?.kycStatus !== 'PENDING' && (\n                    <Button\n                      size=\"sm\"\n                      onClick={() => onTabChange('kyc')}\n                      className=\"bg-solar-600 hover:bg-solar-700 text-white\"\n                    >\n                      Complete KYC\n                    </Button>\n                  )}\n                  <button\n                    onClick={() => setKycBannerDismissed(true)}\n                    className=\"text-solar-500 hover:text-solar-700 p-1\"\n                  >\n                    <X className=\"h-4 w-4\" />\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* Main Content */}\n        <main className=\"flex-1 bg-gray-50 overflow-y-auto\">\n          <div className=\"px-4 sm:px-6 lg:px-8 xl:px-12 py-6\">\n            <div className=\"max-w-7xl mx-auto\">\n              {children}\n            </div>\n          </div>\n        </main>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AARA;;;;;;;;AAgCO,MAAM,kBAAkD,CAAC,EAC9D,QAAQ,EACR,SAAS,EACT,WAAW,EACZ;;IACC,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,cAAc,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAkB;IAE3C,wBAAwB;IACxB,MAAM,sBAAsB;QAC1B;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,+NAAA,CAAA,kBAAe;QAAC;QAC3D;YAAE,IAAI;YAAU,OAAO;YAAgB,MAAM,mMAAA,CAAA,MAAG;QAAC;QACjD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,qNAAA,CAAA,aAAU;QAAC;QACtD;YAAE,IAAI;YAAU,OAAO;YAAU,MAAM,yMAAA,CAAA,SAAM;QAAC;QAC9C;YAAE,IAAI;YAAa,OAAO;YAAa,MAAM,uMAAA,CAAA,QAAK;QAAC;QACnD;YAAE,IAAI;YAAO,OAAO;YAAoB,MAAM,yMAAA,CAAA,SAAM;QAAC;KACtD;IAED,kCAAkC;IAClC,MAAM,kBAAkB,MAAM,SAAS,UACnC;WAAI;QAAqB;YAAE,IAAI;YAAS,OAAO;YAAe,MAAM,6MAAA,CAAA,WAAQ;QAAC;KAAE,GAC/E;IAEJ,MAAM,eAAe;QACnB,MAAM;IACR;IAEA,uCAAuC;IACvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,YAAY,OAAO,IAAI,CAAC,YAAY,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9E,oBAAoB;oBACtB;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG,EAAE;IAEL,qBACE,6LAAC;QAAI,WAAU;;YAEZ,6BACC,6LAAC;gBACC,WAAU;gBACV,SAAS,IAAM,eAAe;;;;;;0BAKlC,6LAAC;gBAAM,WAAW,CAAC;;;QAGjB,EAAE,cAAc,kBAAkB,qCAAqC;MACzE,CAAC;0BACC,cAAA,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,+JAAA,CAAA,UAAI;oCAAC,MAAK;oCAAI,WAAU;;sDACvB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,4IAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;sDAExB,6LAAC;4CAAK,WAAU;sDAAkC;;;;;;;;;;;;8CAEpD,6LAAC;oCACC,SAAS,IAAM,eAAe;oCAC9B,WAAU;8CAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAOjB,6LAAC;4BAAI,WAAU;sCACZ,gBAAgB,GAAG,CAAC,CAAC;gCACpB,MAAM,OAAO,KAAK,IAAI;gCACtB,MAAM,WAAW,cAAc,KAAK,EAAE;gCAEtC,qBACE,6LAAC;oCAEC,SAAS;wCACP,YAAY,KAAK,EAAE;wCACnB,eAAe;oCACjB;oCACA,WAAW,CAAC;;oBAEV,EAAE,WACE,oEACA,sDACH;kBACH,CAAC;;sDAED,6LAAC;4CAAK,WAAW,CAAC,QAAQ,EAAE,WAAW,eAAe,2CAA2C;;;;;;sDACjG,6LAAC;4CAAK,WAAU;sDAAuB,KAAK,KAAK;;;;;;;mCAd5C,KAAK,EAAE;;;;;4BAiBlB;;;;;;sCAIF,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCACC,SAAS;gCACT,WAAU;;kDAEV,6LAAC,6MAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAK,WAAU;kDAAsB;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9C,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAO,WAAU;kCAChB,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,uIAAA,CAAA,OAAI;gCAAC,SAAQ;gCAAU,OAAM;gCAAS,WAAU;;kDAC/C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,SAAS,IAAM,eAAe;gDAC9B,WAAU;0DAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEACX,gBAAgB,IAAI,CAAC,CAAA,OAAQ,KAAK,EAAE,KAAK,YAAY,SAAS;;;;;;kEAEjE,6LAAC;wDAAE,WAAU;kEAAwC;;;;;;;;;;;;;;;;;;kDAMzD,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAO,WAAU;;kEAChB,6LAAC,qMAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6LAAC;wDAAK,WAAU;;;;;;;;;;;;4CAIjB,MAAM,cAAc,cAAc,CAAC,cAAc,SAAS,cAAc,QAAQ,mBAC/E,6LAAC;gDAAI,WAAW,CAAC;;oBAEf,EAAE,MAAM,cAAc,YAClB,gDACA,wCACH;kBACH,CAAC;;oDAAE;oDACK,MAAM;;;;;;;0DAKhB,6LAAC;gDAAI,WAAU;gDAAW,KAAK;;kEAC7B,6LAAC;wDACC,SAAS,IAAM,oBAAoB,CAAC;wDACpC,WAAU;;0EAEV,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAK,WAAU;8EACb,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;0EAG3B,6LAAC,uNAAA,CAAA,cAAW;gEAAC,WAAW,CAAC,2CAA2C,EAAE,mBAAmB,eAAe,IAAI;;;;;;;;;;;;oDAI7G,kCACC,6LAAC;wDAAI,WAAU;;0EAEb,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFACb,cAAA,6LAAC;gFAAK,WAAU;0FACb,MAAM,MAAM,OAAO,GAAG;;;;;;;;;;;sFAG3B,6LAAC;4EAAI,WAAU;;8FACb,6LAAC;oFAAE,WAAU;8FACV,MAAM,MAAM,MAAM,IAAI,CAAC,EAAE;;;;;;8FAE5B,6LAAC;oFAAE,WAAU;;wFAAwB;wFAC9B,MAAM;;;;;;;;;;;;;;;;;;;;;;;;0EAOnB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,SAAS;4EACP,oBAAoB;wEACpB,mCAAmC;wEACrC;wEACA,WAAU;;0FAEV,6LAAC,qMAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC;wEACC,SAAS;4EACP,oBAAoB;4EACpB,YAAY;wEACd;wEACA,WAAU;;0FAEV,6LAAC,qNAAA,CAAA,aAAU;gFAAC,WAAU;;;;;;0FACtB,6LAAC;0FAAK;;;;;;;;;;;;kFAER,6LAAC;wEAAI,WAAU;;;;;;kFACf,6LAAC;wEACC,SAAS;4EACP,oBAAoB;4EACpB;wEACF;wEACA,WAAU;;0FAEV,6LAAC,6MAAA,CAAA,SAAM;gFAAC,WAAU;;;;;;0FAClB,6LAAC;0FAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;oBAYvB,MAAM,cAAc,cAAc,CAAC,oCAClC,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,uNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,6LAAC;;kEACC,6LAAC;wDAAE,WAAU;kEACV,MAAM,cAAc,YACjB,iCACA;;;;;;kEAGN,6LAAC;wDAAE,WAAU;kEACV,MAAM,cAAc,YACjB,6EACA;;;;;;;;;;;;;;;;;;kDAKV,6LAAC;wCAAI,WAAU;;4CACZ,MAAM,cAAc,2BACnB,6LAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAS,IAAM,YAAY;gDAC3B,WAAU;0DACX;;;;;;0DAIH,6LAAC;gDACC,SAAS,IAAM,sBAAsB;gDACrC,WAAU;0DAEV,cAAA,6LAAC,+LAAA,CAAA,IAAC;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASzB,6LAAC;wBAAK,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;0CACZ;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOf;GAxSa;;QAKc,2HAAA,CAAA,UAAO;;;KALrB", "debugId": null}}, {"offset": {"line": 2542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/DashboardOverview.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardTitle, CardContent, Button } from '@/components/ui';\nimport { Grid, GridItem } from '@/components/layout';\nimport { MiningRig, Cryptocurrency, SolarPanel } from '@/components/icons';\nimport { TrendingUp, Wallet, Users, Zap, Clock, Award } from 'lucide-react';\nimport { formatCurrency, formatTHS, getTimeUntilNextPayout } from '@/lib/utils';\n\ninterface DashboardStats {\n  totalTHS: number;\n  estimatedEarnings: {\n    next7Days: number;\n    next30Days: number;\n    next365Days: number;\n  };\n  walletBalance: number;\n  pendingEarnings: number;\n  activeUnits: number;\n  totalEarnings: number;\n  directReferrals: number;\n  binaryPoints: {\n    leftPoints: number;\n    rightPoints: number;\n  };\n}\n\nexport const DashboardOverview: React.FC = () => {\n  const [stats, setStats] = useState<DashboardStats | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [timeUntilPayout, setTimeUntilPayout] = useState(getTimeUntilNextPayout());\n\n  useEffect(() => {\n    fetchDashboardStats();\n    \n    // Update countdown every second\n    const interval = setInterval(() => {\n      setTimeUntilPayout(getTimeUntilNextPayout());\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchDashboardStats = async () => {\n    try {\n      // Fetch data from multiple endpoints\n      const [walletRes, earningsRes, miningRes, referralRes] = await Promise.all([\n        fetch('/api/wallet/balance', { credentials: 'include' }),\n        fetch('/api/earnings', { credentials: 'include' }),\n        fetch('/api/mining-units', { credentials: 'include' }),\n        fetch('/api/referrals/tree?depth=1', { credentials: 'include' }),\n      ]);\n\n      const [walletData, earningsData, miningData, referralData] = await Promise.all([\n        walletRes.json(),\n        earningsRes.json(),\n        miningRes.json(),\n        referralRes.json(),\n      ]);\n\n      if (walletData.success && earningsData.success && miningData.success && referralData.success) {\n        const totalTHS = miningData.data.reduce((sum: number, unit: any) => sum + unit.thsAmount, 0);\n        \n        setStats({\n          totalTHS,\n          estimatedEarnings: earningsData.data.estimatedEarnings,\n          walletBalance: walletData.data.balance,\n          pendingEarnings: walletData.data.pendingEarnings,\n          activeUnits: miningData.data.length,\n          totalEarnings: earningsData.data.totalEarnings,\n          directReferrals: referralData.data.statistics.totalDirectReferrals,\n          binaryPoints: referralData.data.statistics.binaryPoints,\n        });\n      }\n    } catch (error) {\n      console.error('Failed to fetch dashboard stats:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        {Array.from({ length: 3 }).map((_, i) => (\n          <div key={i} className=\"animate-pulse\">\n            <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  if (!stats) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load dashboard data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Welcome Section */}\n      <div className=\"bg-gradient-to-r from-solar-500 to-eco-500 rounded-2xl p-8 text-white shadow-lg\">\n        <h1 className=\"text-3xl font-bold mb-3\">Welcome to HashCoreX</h1>\n        <p className=\"text-solar-100 text-lg leading-relaxed\">\n          Your sustainable mining dashboard. Track your earnings, manage your mining units, and grow your referral network.\n        </p>\n      </div>\n\n      {/* Key Metrics */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Key Metrics</h2>\n        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Total Mining Power</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatTHS(stats.totalTHS)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Zap className=\"h-7 w-7 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Wallet Balance</p>\n                  <p className=\"text-3xl font-bold text-eco-600\">\n                    {formatCurrency(stats.walletBalance)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <Wallet className=\"h-7 w-7 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Total Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(stats.totalEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center\">\n                  <TrendingUp className=\"h-7 w-7 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Direct Referrals</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {stats.directReferrals}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-purple-100 rounded-xl flex items-center justify-center\">\n                  <Users className=\"h-7 w-7 text-purple-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Earnings and Payout Section */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Earnings Overview</h2>\n        <Grid cols={{ default: 1, lg: 2 }} gap={8}>\n          {/* Estimated Earnings */}\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardHeader className=\"pb-4\">\n              <CardTitle className=\"flex items-center space-x-3 text-lg\">\n                <div className=\"h-10 w-10 bg-eco-100 rounded-lg flex items-center justify-center\">\n                  <TrendingUp className=\"h-5 w-5 text-eco-600\" />\n                </div>\n                <span>Estimated Earnings</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"pt-0\">\n              <div className=\"space-y-5\">\n                <div className=\"flex justify-between items-center py-2\">\n                  <span className=\"text-gray-600 font-medium\">Next 7 Days</span>\n                  <span className=\"font-bold text-eco-600 text-lg\">\n                    {formatCurrency(stats.estimatedEarnings.next7Days)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center py-2\">\n                  <span className=\"text-gray-600 font-medium\">Next 30 Days</span>\n                  <span className=\"font-bold text-eco-600 text-lg\">\n                    {formatCurrency(stats.estimatedEarnings.next30Days)}\n                  </span>\n                </div>\n                <div className=\"flex justify-between items-center py-2\">\n                  <span className=\"text-gray-600 font-medium\">Next 365 Days</span>\n                  <span className=\"font-bold text-eco-600 text-lg\">\n                    {formatCurrency(stats.estimatedEarnings.next365Days)}\n                  </span>\n                </div>\n              </div>\n              <div className=\"mt-6 p-4 bg-eco-50 rounded-xl\">\n                <p className=\"text-sm text-eco-700 font-medium\">\n                  * Based on current mining units and average ROI\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Next Payout */}\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardHeader className=\"pb-4\">\n              <CardTitle className=\"flex items-center space-x-3 text-lg\">\n                <div className=\"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center\">\n                  <Clock className=\"h-5 w-5 text-solar-600\" />\n                </div>\n                <span>Next Payout</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent className=\"pt-0\">\n              <div className=\"text-center\">\n                <p className=\"text-sm text-gray-600 mb-6 font-medium\">\n                  Weekly payout every Saturday at 15:00 UTC\n                </p>\n                <div className=\"grid grid-cols-4 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.days}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Days</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.hours}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Hours</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.minutes}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Min</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"bg-solar-50 rounded-xl p-3 mb-2\">\n                      <div className=\"text-2xl font-bold text-solar-600\">\n                        {timeUntilPayout.seconds}\n                      </div>\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Sec</div>\n                  </div>\n                </div>\n                {stats.pendingEarnings > 0 && (\n                  <div className=\"mt-6 p-4 bg-solar-50 rounded-xl\">\n                    <p className=\"text-sm text-solar-700 font-semibold\">\n                      <strong className=\"text-lg\">{formatCurrency(stats.pendingEarnings)}</strong> pending\n                    </p>\n                  </div>\n                )}\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Quick Actions */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Quick Actions</h2>\n        <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n          <CardContent className=\"p-8\">\n            <Grid cols={{ default: 1, sm: 2, lg: 3 }} gap={6}>\n              <Button className=\"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl\">\n                <MiningRig className=\"h-7 w-7\" />\n                <span>Buy Mining Power</span>\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 hover:bg-gray-50\">\n                <Cryptocurrency className=\"h-7 w-7\" />\n                <span>Withdraw USDT</span>\n              </Button>\n              <Button variant=\"outline\" className=\"h-20 flex flex-col items-center justify-center space-y-3 text-base font-semibold rounded-xl border-2 hover:bg-gray-50\">\n                <Users className=\"h-7 w-7\" />\n                <span>Share Referral</span>\n              </Button>\n            </Grid>\n          </CardContent>\n        </Card>\n      </div>\n\n      {/* Binary Points Summary */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Binary Network Summary</h2>\n        <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n          <CardHeader className=\"pb-4\">\n            <CardTitle className=\"flex items-center space-x-3 text-lg\">\n              <div className=\"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center\">\n                <Award className=\"h-5 w-5 text-solar-600\" />\n              </div>\n              <span>Network Performance</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent className=\"pt-0\">\n            <Grid cols={{ default: 1, sm: 3 }} gap={8}>\n              <div className=\"text-center\">\n                <div className=\"bg-solar-50 rounded-xl p-6 mb-3\">\n                  <div className=\"text-3xl font-bold text-solar-600\">\n                    {stats.binaryPoints.leftPoints}\n                  </div>\n                </div>\n                <div className=\"text-sm text-gray-600 font-medium\">Left Points</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"bg-solar-50 rounded-xl p-6 mb-3\">\n                  <div className=\"text-3xl font-bold text-solar-600\">\n                    {stats.binaryPoints.rightPoints}\n                  </div>\n                </div>\n                <div className=\"text-sm text-gray-600 font-medium\">Right Points</div>\n              </div>\n              <div className=\"text-center\">\n                <div className=\"bg-eco-50 rounded-xl p-6 mb-3\">\n                  <div className=\"text-3xl font-bold text-eco-600\">\n                    {Math.min(stats.binaryPoints.leftPoints, stats.binaryPoints.rightPoints)}\n                  </div>\n                </div>\n                <div className=\"text-sm text-gray-600 font-medium\">Potential Match</div>\n              </div>\n            </Grid>\n          </CardContent>\n        </Card>\n      </div>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AAPA;;;;;;;AA2BO,MAAM,oBAA8B;;IACzC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;IAE5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR;YAEA,gCAAgC;YAChC,MAAM,WAAW;wDAAY;oBAC3B,mBAAmB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;gBAC1C;uDAAG;YAEH;+CAAO,IAAM,cAAc;;QAC7B;sCAAG,EAAE;IAEL,MAAM,sBAAsB;QAC1B,IAAI;YACF,qCAAqC;YACrC,MAAM,CAAC,WAAW,aAAa,WAAW,YAAY,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACzE,MAAM,uBAAuB;oBAAE,aAAa;gBAAU;gBACtD,MAAM,iBAAiB;oBAAE,aAAa;gBAAU;gBAChD,MAAM,qBAAqB;oBAAE,aAAa;gBAAU;gBACpD,MAAM,+BAA+B;oBAAE,aAAa;gBAAU;aAC/D;YAED,MAAM,CAAC,YAAY,cAAc,YAAY,aAAa,GAAG,MAAM,QAAQ,GAAG,CAAC;gBAC7E,UAAU,IAAI;gBACd,YAAY,IAAI;gBAChB,UAAU,IAAI;gBACd,YAAY,IAAI;aACjB;YAED,IAAI,WAAW,OAAO,IAAI,aAAa,OAAO,IAAI,WAAW,OAAO,IAAI,aAAa,OAAO,EAAE;gBAC5F,MAAM,WAAW,WAAW,IAAI,CAAC,MAAM,CAAC,CAAC,KAAa,OAAc,MAAM,KAAK,SAAS,EAAE;gBAE1F,SAAS;oBACP;oBACA,mBAAmB,aAAa,IAAI,CAAC,iBAAiB;oBACtD,eAAe,WAAW,IAAI,CAAC,OAAO;oBACtC,iBAAiB,WAAW,IAAI,CAAC,eAAe;oBAChD,aAAa,WAAW,IAAI,CAAC,MAAM;oBACnC,eAAe,aAAa,IAAI,CAAC,aAAa;oBAC9C,iBAAiB,aAAa,IAAI,CAAC,UAAU,CAAC,oBAAoB;oBAClE,cAAc,aAAa,IAAI,CAAC,UAAU,CAAC,YAAY;gBACzD;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACZ,MAAM,IAAI,CAAC;gBAAE,QAAQ;YAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;oBAAY,WAAU;8BACrB,cAAA,6LAAC;wBAAI,WAAU;;;;;;mBADP;;;;;;;;;;IAMlB;IAEA,IAAI,CAAC,OAAO;QACV,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAAyC;;;;;;;;;;;;0BAMxD,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,MAAM,QAAQ;;;;;;;;;;;;0DAG7B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,mMAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMvB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,aAAa;;;;;;;;;;;;0DAGvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM1B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,aAAa;;;;;;;;;;;;0DAGvC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,MAAM,eAAe;;;;;;;;;;;;0DAG1B,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7B,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAEtC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;wDAAC,WAAU;;;;;;;;;;;8DAExB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB,CAAC,SAAS;;;;;;;;;;;;kEAGrD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB,CAAC,UAAU;;;;;;;;;;;;kEAGtD,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAK,WAAU;0EAA4B;;;;;;0EAC5C,6LAAC;gEAAK,WAAU;0EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,iBAAiB,CAAC,WAAW;;;;;;;;;;;;;;;;;;0DAIzD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;8DAAmC;;;;;;;;;;;;;;;;;;;;;;;0CAQtD,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDACd,6LAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;8DAEnB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;kDACrB,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAGtD,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,IAAI;;;;;;;;;;;8EAGzB,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAErD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,KAAK;;;;;;;;;;;8EAG1B,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAErD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,OAAO;;;;;;;;;;;8EAG5B,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;sEAErD,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACb,cAAA,6LAAC;wEAAI,WAAU;kFACZ,gBAAgB,OAAO;;;;;;;;;;;8EAG5B,6LAAC;oEAAI,WAAU;8EAAoC;;;;;;;;;;;;;;;;;;gDAGtD,MAAM,eAAe,GAAG,mBACvB,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;gEAAO,WAAU;0EAAW,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,MAAM,eAAe;;;;;;4DAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAW5F,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6LAAC,uIAAA,CAAA,OAAI;gCAAC,MAAM;oCAAE,SAAS;oCAAG,IAAI;oCAAG,IAAI;gCAAE;gCAAG,KAAK;;kDAC7C,6LAAC,qIAAA,CAAA,SAAM;wCAAC,WAAU;;0DAChB,6LAAC,2IAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;0DACrB,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,gJAAA,CAAA,iBAAc;gDAAC,WAAU;;;;;;0DAC1B,6LAAC;0DAAK;;;;;;;;;;;;kDAER,6LAAC,qIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,WAAU;;0DAClC,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;0DACjB,6LAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhB,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,mIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,6LAAC,mIAAA,CAAA,aAAU;gCAAC,WAAU;0CACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;gDAAC,WAAU;;;;;;;;;;;sDAEnB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6LAAC,uIAAA,CAAA,OAAI;oCAAC,MAAM;wCAAE,SAAS;wCAAG,IAAI;oCAAE;oCAAG,KAAK;;sDACtC,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,MAAM,YAAY,CAAC,UAAU;;;;;;;;;;;8DAGlC,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;8DAGnC,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;sDAErD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAU;kEACZ,KAAK,GAAG,CAAC,MAAM,YAAY,CAAC,UAAU,EAAE,MAAM,YAAY,CAAC,WAAW;;;;;;;;;;;8DAG3E,6LAAC;oDAAI,WAAU;8DAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQnE;GAxUa;KAAA", "debugId": null}}, {"offset": {"line": 3714, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/PurchaseMiningUnit.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Input, Card, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { MiningRig, Cryptocurrency } from '@/components/icons';\nimport { Calculator, DollarSign, Zap } from 'lucide-react';\nimport { formatCurrency, formatNumber } from '@/lib/utils';\n\ninterface PurchaseMiningUnitProps {\n  onPurchaseComplete?: () => void;\n}\n\nexport const PurchaseMiningUnit: React.FC<PurchaseMiningUnitProps> = ({\n  onPurchaseComplete,\n}) => {\n  const [formData, setFormData] = useState({\n    thsAmount: '',\n    investmentAmount: '',\n  });\n  const [loading, setLoading] = useState(false);\n  const [error, setError] = useState('');\n  const [thsPrice, setThsPrice] = useState(50); // Default price\n  const [roiRange, setRoiRange] = useState({ min: 0.6, max: 1.1 });\n\n  // Fetch current TH/s price and ROI range\n  useEffect(() => {\n    fetchPricing();\n  }, []);\n\n  const fetchPricing = async () => {\n    try {\n      const response = await fetch('/api/admin/settings/pricing');\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setThsPrice(data.data.thsPrice);\n          setRoiRange(data.data.roiRange);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch pricing:', error);\n    }\n  };\n\n  const handleThsChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const ths = parseFloat(e.target.value) || 0;\n    const investment = ths * thsPrice;\n    \n    setFormData({\n      thsAmount: e.target.value,\n      investmentAmount: investment > 0 ? investment.toFixed(2) : '',\n    });\n  };\n\n  const handleInvestmentChange = (e: React.ChangeEvent<HTMLInputElement>) => {\n    const investment = parseFloat(e.target.value) || 0;\n    const ths = investment / thsPrice;\n    \n    setFormData({\n      thsAmount: ths > 0 ? ths.toFixed(4) : '',\n      investmentAmount: e.target.value,\n    });\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setError('');\n    setLoading(true);\n\n    try {\n      const thsAmount = parseFloat(formData.thsAmount);\n      const investmentAmount = parseFloat(formData.investmentAmount);\n\n      if (!thsAmount || !investmentAmount || thsAmount <= 0 || investmentAmount <= 0) {\n        throw new Error('Please enter valid amounts');\n      }\n\n      if (investmentAmount < 50) {\n        throw new Error('Minimum purchase amount is $50');\n      }\n\n      const response = await fetch('/api/mining-units', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          thsAmount,\n          investmentAmount,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Purchase failed');\n      }\n\n      // Reset form\n      setFormData({\n        thsAmount: '',\n        investmentAmount: '',\n      });\n\n      // Notify parent component\n      if (onPurchaseComplete) {\n        onPurchaseComplete();\n      }\n\n    } catch (err: any) {\n      setError(err.message || 'Purchase failed');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateEstimatedEarnings = () => {\n    const investment = parseFloat(formData.investmentAmount) || 0;\n    if (investment <= 0) return { daily: 0, weekly: 0, monthly: 0 };\n\n    const avgROI = (roiRange.min + roiRange.max) / 2;\n    const daily = (investment * avgROI) / 100;\n    \n    return {\n      daily,\n      weekly: daily * 7,\n      monthly: daily * 30,\n    };\n  };\n\n  const estimatedEarnings = calculateEstimatedEarnings();\n\n  return (\n    <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n      <CardHeader className=\"pb-4\">\n        <CardTitle className=\"flex items-center space-x-3 text-xl\">\n          <div className=\"h-10 w-10 bg-solar-100 rounded-lg flex items-center justify-center\">\n            <MiningRig className=\"h-6 w-6 text-solar-600\" />\n          </div>\n          <span>Purchase Mining Power</span>\n        </CardTitle>\n      </CardHeader>\n      <CardContent className=\"pt-0\">\n        <form onSubmit={handleSubmit} className=\"space-y-8\">\n          {error && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-xl text-sm font-medium\">\n              {error}\n            </div>\n          )}\n\n          {/* Current Pricing Info */}\n          <div className=\"bg-gradient-to-r from-solar-50 to-eco-50 rounded-xl p-6\">\n            <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4\">\n              <div className=\"text-center sm:text-left\">\n                <span className=\"text-sm font-medium text-gray-600 block mb-1\">Current TH/s Price</span>\n                <span className=\"text-2xl font-bold text-solar-600\">\n                  {formatCurrency(thsPrice)} / TH/s\n                </span>\n              </div>\n              <div className=\"text-center sm:text-right\">\n                <span className=\"text-sm font-medium text-gray-600 block mb-1\">Daily ROI Range</span>\n                <span className=\"text-xl font-bold text-eco-600\">\n                  {roiRange.min}% - {roiRange.max}%\n                </span>\n              </div>\n            </div>\n          </div>\n\n          {/* Input Fields */}\n          <div className=\"space-y-6\">\n            <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n              <div>\n                <Input\n                  label=\"TH/s Amount\"\n                  type=\"number\"\n                  step=\"0.0001\"\n                  min=\"0\"\n                  value={formData.thsAmount}\n                  onChange={handleThsChange}\n                  placeholder=\"Enter TH/s amount\"\n                  leftIcon={<Zap className=\"h-4 w-4\" />}\n                  className=\"h-12\"\n                />\n              </div>\n\n              <div>\n                <Input\n                  label=\"Investment Amount (USD)\"\n                  type=\"number\"\n                  step=\"0.01\"\n                  min=\"50\"\n                  value={formData.investmentAmount}\n                  onChange={handleInvestmentChange}\n                  placeholder=\"Enter investment amount\"\n                  leftIcon={<DollarSign className=\"h-4 w-4\" />}\n                  className=\"h-12\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Estimated Earnings */}\n          {estimatedEarnings.daily > 0 && (\n            <div className=\"bg-gradient-to-r from-eco-50 to-green-50 rounded-xl p-6\">\n              <h4 className=\"text-base font-semibold text-gray-800 mb-4 flex items-center\">\n                <div className=\"h-8 w-8 bg-eco-100 rounded-lg flex items-center justify-center mr-3\">\n                  <Calculator className=\"h-4 w-4 text-eco-600\" />\n                </div>\n                Estimated Earnings (Average ROI: {formatNumber((roiRange.min + roiRange.max) / 2, 1)}%)\n              </h4>\n              <div className=\"grid grid-cols-1 sm:grid-cols-3 gap-6\">\n                <div className=\"text-center\">\n                  <div className=\"bg-white rounded-xl p-4 shadow-sm\">\n                    <div className=\"text-2xl font-bold text-eco-600 mb-1\">\n                      {formatCurrency(estimatedEarnings.daily)}\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Daily</div>\n                  </div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"bg-white rounded-xl p-4 shadow-sm\">\n                    <div className=\"text-2xl font-bold text-eco-600 mb-1\">\n                      {formatCurrency(estimatedEarnings.weekly)}\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Weekly</div>\n                  </div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"bg-white rounded-xl p-4 shadow-sm\">\n                    <div className=\"text-2xl font-bold text-eco-600 mb-1\">\n                      {formatCurrency(estimatedEarnings.monthly)}\n                    </div>\n                    <div className=\"text-sm text-gray-600 font-medium\">Monthly</div>\n                  </div>\n                </div>\n              </div>\n            </div>\n          )}\n\n          {/* Important Notes */}\n          <div className=\"bg-gray-50 rounded-xl p-6\">\n            <h4 className=\"text-base font-semibold text-gray-800 mb-4\">Important Notes:</h4>\n            <ul className=\"text-sm text-gray-600 space-y-2\">\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Minimum purchase: $50</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Mining units are active for 12 months</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Units expire when 5x investment is earned</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>Weekly payouts every Saturday at 15:00 UTC</span>\n              </li>\n              <li className=\"flex items-start\">\n                <span className=\"text-solar-500 mr-2\">•</span>\n                <span>ROI varies daily based on mining performance</span>\n              </li>\n            </ul>\n          </div>\n\n          <Button\n            type=\"submit\"\n            size=\"lg\"\n            className=\"w-full h-14 text-lg font-semibold rounded-xl\"\n            loading={loading}\n            disabled={!formData.thsAmount || !formData.investmentAmount || parseFloat(formData.investmentAmount) < 50}\n          >\n            <Cryptocurrency className=\"h-6 w-6 mr-3\" />\n            Purchase Mining Unit\n          </Button>\n        </form>\n      </CardContent>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;;;AANA;;;;;;AAYO,MAAM,qBAAwD,CAAC,EACpE,kBAAkB,EACnB;;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,WAAW;QACX,kBAAkB;IACpB;IACA,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACnC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,KAAK,gBAAgB;IAC9D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,KAAK;QAAK,KAAK;IAAI;IAE9D,yCAAyC;IACzC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;wCAAE;YACR;QACF;uCAAG,EAAE;IAEL,MAAM,eAAe;QACnB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM;YAC7B,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI,CAAC,QAAQ;oBAC9B,YAAY,KAAK,IAAI,CAAC,QAAQ;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;QAC5C;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,MAAM,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;QAC1C,MAAM,aAAa,MAAM;QAEzB,YAAY;YACV,WAAW,EAAE,MAAM,CAAC,KAAK;YACzB,kBAAkB,aAAa,IAAI,WAAW,OAAO,CAAC,KAAK;QAC7D;IACF;IAEA,MAAM,yBAAyB,CAAC;QAC9B,MAAM,aAAa,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;QACjD,MAAM,MAAM,aAAa;QAEzB,YAAY;YACV,WAAW,MAAM,IAAI,IAAI,OAAO,CAAC,KAAK;YACtC,kBAAkB,EAAE,MAAM,CAAC,KAAK;QAClC;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,YAAY,WAAW,SAAS,SAAS;YAC/C,MAAM,mBAAmB,WAAW,SAAS,gBAAgB;YAE7D,IAAI,CAAC,aAAa,CAAC,oBAAoB,aAAa,KAAK,oBAAoB,GAAG;gBAC9E,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,mBAAmB,IAAI;gBACzB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA;gBACF;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,aAAa;YACb,YAAY;gBACV,WAAW;gBACX,kBAAkB;YACpB;YAEA,0BAA0B;YAC1B,IAAI,oBAAoB;gBACtB;YACF;QAEF,EAAE,OAAO,KAAU;YACjB,SAAS,IAAI,OAAO,IAAI;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,6BAA6B;QACjC,MAAM,aAAa,WAAW,SAAS,gBAAgB,KAAK;QAC5D,IAAI,cAAc,GAAG,OAAO;YAAE,OAAO;YAAG,QAAQ;YAAG,SAAS;QAAE;QAE9D,MAAM,SAAS,CAAC,SAAS,GAAG,GAAG,SAAS,GAAG,IAAI;QAC/C,MAAM,QAAQ,AAAC,aAAa,SAAU;QAEtC,OAAO;YACL;YACA,QAAQ,QAAQ;YAChB,SAAS,QAAQ;QACnB;IACF;IAEA,MAAM,oBAAoB;IAE1B,qBACE,6LAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,6LAAC,mIAAA,CAAA,aAAU;gBAAC,WAAU;0BACpB,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC,2IAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;;;;;;sCAEvB,6LAAC;sCAAK;;;;;;;;;;;;;;;;;0BAGV,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAK,UAAU;oBAAc,WAAU;;wBACrC,uBACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAKL,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA+C;;;;;;0DAC/D,6LAAC;gDAAK,WAAU;;oDACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE;oDAAU;;;;;;;;;;;;;kDAG9B,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAK,WAAU;0DAA+C;;;;;;0DAC/D,6LAAC;gDAAK,WAAU;;oDACb,SAAS,GAAG;oDAAC;oDAAK,SAAS,GAAG;oDAAC;;;;;;;;;;;;;;;;;;;;;;;;sCAOxC,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;kDACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,KAAI;4CACJ,OAAO,SAAS,SAAS;4CACzB,UAAU;4CACV,aAAY;4CACZ,wBAAU,6LAAC,mMAAA,CAAA,MAAG;gDAAC,WAAU;;;;;;4CACzB,WAAU;;;;;;;;;;;kDAId,6LAAC;kDACC,cAAA,6LAAC,oIAAA,CAAA,QAAK;4CACJ,OAAM;4CACN,MAAK;4CACL,MAAK;4CACL,KAAI;4CACJ,OAAO,SAAS,gBAAgB;4CAChC,UAAU;4CACV,aAAY;4CACZ,wBAAU,6LAAC,qNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;4CAChC,WAAU;;;;;;;;;;;;;;;;;;;;;;wBAOjB,kBAAkB,KAAK,GAAG,mBACzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC,iNAAA,CAAA,aAAU;gDAAC,WAAU;;;;;;;;;;;wCAClB;wCAC4B,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,CAAC,SAAS,GAAG,GAAG,SAAS,GAAG,IAAI,GAAG;wCAAG;;;;;;;8CAEvF,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,KAAK;;;;;;kEAEzC,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,MAAM;;;;;;kEAE1C,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;sDAGvD,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,kBAAkB,OAAO;;;;;;kEAE3C,6LAAC;wDAAI,WAAU;kEAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAQ7D,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA6C;;;;;;8CAC3D,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;sDAER,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAK,WAAU;8DAAsB;;;;;;8DACtC,6LAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;;sCAKZ,6LAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,MAAK;4BACL,WAAU;4BACV,SAAS;4BACT,UAAU,CAAC,SAAS,SAAS,IAAI,CAAC,SAAS,gBAAgB,IAAI,WAAW,SAAS,gBAAgB,IAAI;;8CAEvG,6LAAC,gJAAA,CAAA,iBAAc;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;;;;;;;AAOvD;GA7Qa;KAAA", "debugId": null}}, {"offset": {"line": 4382, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/EarningsTracker.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Card, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { Grid, GridItem } from '@/components/layout';\nimport { TrendingUp, DollarSign, Clock, Calendar } from 'lucide-react';\nimport { formatCurrency, formatDateTime, getTimeUntilNextPayout } from '@/lib/utils';\n\ninterface EarningsData {\n  totalEarnings: number;\n  pendingEarnings: number;\n  miningEarnings: number;\n  referralEarnings: number;\n  estimatedEarnings: {\n    next7Days: number;\n    next30Days: number;\n    next365Days: number;\n  };\n  recentEarnings: Array<{\n    id: string;\n    type: string;\n    amount: number;\n    description: string;\n    createdAt: string;\n  }>;\n  earningsBreakdown: {\n    mining: number;\n    directReferral: number;\n    binaryBonus: number;\n  };\n}\n\nexport const EarningsTracker: React.FC = () => {\n  const [earningsData, setEarningsData] = useState<EarningsData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [timeUntilPayout, setTimeUntilPayout] = useState(getTimeUntilNextPayout());\n\n  useEffect(() => {\n    fetchEarningsData();\n    \n    // Update countdown every second\n    const interval = setInterval(() => {\n      setTimeUntilPayout(getTimeUntilNextPayout());\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchEarningsData = async () => {\n    try {\n      const response = await fetch('/api/earnings', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setEarningsData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch earnings data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!earningsData) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load earnings data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  const getEarningsTypeColor = (type: string) => {\n    switch (type) {\n      case 'MINING_EARNINGS':\n        return 'text-solar-600';\n      case 'DIRECT_REFERRAL':\n        return 'text-eco-600';\n      case 'BINARY_BONUS':\n        return 'text-blue-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getEarningsTypeLabel = (type: string) => {\n    switch (type) {\n      case 'MINING_EARNINGS':\n        return 'Mining';\n      case 'DIRECT_REFERRAL':\n        return 'Direct Referral';\n      case 'BINARY_BONUS':\n        return 'Binary Bonus';\n      default:\n        return type;\n    }\n  };\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Earnings Overview */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Earnings Overview</h2>\n        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Total Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(earningsData.totalEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <DollarSign className=\"h-7 w-7 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Pending Earnings</p>\n                  <p className=\"text-3xl font-bold text-solar-600\">\n                    {formatCurrency(earningsData.pendingEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Clock className=\"h-7 w-7 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Mining Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(earningsData.miningEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-gray-100 rounded-xl flex items-center justify-center\">\n                  <TrendingUp className=\"h-7 w-7 text-gray-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Referral Earnings</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {formatCurrency(earningsData.referralEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center\">\n                  <TrendingUp className=\"h-7 w-7 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      <Grid cols={{ default: 1, lg: 2 }} gap={6}>\n        {/* Next Payout Countdown */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Calendar className=\"h-5 w-5 text-solar-500\" />\n              <span>Next Payout</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"text-center\">\n              <p className=\"text-sm text-gray-600 mb-4\">\n                Weekly payout every Saturday at 15:00 UTC\n              </p>\n              <div className=\"grid grid-cols-4 gap-4\">\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.days}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Days</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.hours}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Hours</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.minutes}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Minutes</div>\n                </div>\n                <div className=\"text-center\">\n                  <div className=\"text-2xl font-bold text-solar-600\">\n                    {timeUntilPayout.seconds}\n                  </div>\n                  <div className=\"text-xs text-gray-500\">Seconds</div>\n                </div>\n              </div>\n              {earningsData.pendingEarnings > 0 && (\n                <div className=\"mt-4 p-3 bg-solar-50 rounded-lg\">\n                  <p className=\"text-sm text-solar-700\">\n                    <strong>{formatCurrency(earningsData.pendingEarnings)}</strong> will be transferred to your wallet\n                  </p>\n                </div>\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Estimated Earnings */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <TrendingUp className=\"h-5 w-5 text-eco-500\" />\n              <span>Estimated Earnings</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-4\">\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Next 7 Days</span>\n                <span className=\"font-semibold text-eco-600\">\n                  {formatCurrency(earningsData.estimatedEarnings.next7Days)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Next 30 Days</span>\n                <span className=\"font-semibold text-eco-600\">\n                  {formatCurrency(earningsData.estimatedEarnings.next30Days)}\n                </span>\n              </div>\n              <div className=\"flex justify-between items-center\">\n                <span className=\"text-gray-600\">Next 365 Days</span>\n                <span className=\"font-semibold text-eco-600\">\n                  {formatCurrency(earningsData.estimatedEarnings.next365Days)}\n                </span>\n              </div>\n            </div>\n            <div className=\"mt-4 p-3 bg-gray-50 rounded-lg\">\n              <p className=\"text-xs text-gray-600\">\n                * Estimates based on current mining units and average ROI\n              </p>\n            </div>\n          </CardContent>\n        </Card>\n      </Grid>\n\n      {/* Recent Earnings */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Earnings</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {earningsData.recentEarnings.length > 0 ? (\n            <div className=\"space-y-3\">\n              {earningsData.recentEarnings.slice(0, 10).map((earning) => (\n                <div key={earning.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className={`text-sm font-medium ${getEarningsTypeColor(earning.type)}`}>\n                        {getEarningsTypeLabel(earning.type)}\n                      </span>\n                      <span className=\"text-xs text-gray-500\">\n                        {formatDateTime(earning.createdAt)}\n                      </span>\n                    </div>\n                    <p className=\"text-sm text-gray-600 mt-1\">{earning.description}</p>\n                  </div>\n                  <div className=\"text-right\">\n                    <span className=\"font-semibold text-eco-600\">\n                      +{formatCurrency(earning.amount)}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No earnings yet</p>\n              <p className=\"text-sm text-gray-400 mt-1\">\n                Purchase mining units to start earning\n              </p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAgCO,MAAM,kBAA4B;;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;IAE5E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YAEA,gCAAgC;YAChC,MAAM,WAAW;sDAAY;oBAC3B,mBAAmB,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD;gBAC1C;qDAAG;YAEH;6CAAO,IAAM,cAAc;;QAC7B;oCAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB,KAAK,IAAI;gBAC3B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,cAAc;QACjB,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,aAAa;;;;;;;;;;;;0DAG9C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,eAAe;;;;;;;;;;;;0DAGhD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,cAAc;;;;;;;;;;;;0DAG/C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,gBAAgB;;;;;;;;;;;;0DAGjD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQlC,6LAAC,uIAAA,CAAA,OAAI;gBAAC,MAAM;oBAAE,SAAS;oBAAG,IAAI;gBAAE;gBAAG,KAAK;;kCAEtC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,6MAAA,CAAA,WAAQ;4CAAC,WAAU;;;;;;sDACpB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,mIAAA,CAAA,cAAW;0CACV,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDAA6B;;;;;;sDAG1C,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,IAAI;;;;;;sEAEvB,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,KAAK;;;;;;sEAExB,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,OAAO;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;8DAEzC,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEACZ,gBAAgB,OAAO;;;;;;sEAE1B,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;;;;;;;;;;;;;wCAG1C,aAAa,eAAe,GAAG,mBAC9B,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAE,WAAU;;kEACX,6LAAC;kEAAQ,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,eAAe;;;;;;oDAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAS3E,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,qNAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;sDACtB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,iBAAiB,CAAC,SAAS;;;;;;;;;;;;0DAG5D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,iBAAiB,CAAC,UAAU;;;;;;;;;;;;0DAG7D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,aAAa,iBAAiB,CAAC,WAAW;;;;;;;;;;;;;;;;;;kDAIhE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAS7C,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACT,aAAa,cAAc,CAAC,MAAM,GAAG,kBACpC,6LAAC;4BAAI,WAAU;sCACZ,aAAa,cAAc,CAAC,KAAK,CAAC,GAAG,IAAI,GAAG,CAAC,CAAC,wBAC7C,6LAAC;oCAAqB,WAAU;;sDAC9B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAK,WAAW,CAAC,oBAAoB,EAAE,qBAAqB,QAAQ,IAAI,GAAG;sEACzE,qBAAqB,QAAQ,IAAI;;;;;;sEAEpC,6LAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;8DAGrC,6LAAC;oDAAE,WAAU;8DAA8B,QAAQ,WAAW;;;;;;;;;;;;sDAEhE,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDAAK,WAAU;;oDAA6B;oDACzC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,QAAQ,MAAM;;;;;;;;;;;;;mCAd3B,QAAQ,EAAE;;;;;;;;;iDAqBxB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAE,WAAU;8CAAgB;;;;;;8CAC7B,6LAAC;oCAAE,WAAU;8CAA6B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASxD;GA3Ra;KAAA", "debugId": null}}, {"offset": {"line": 5288, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/WalletDashboard.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { Button, Input, Card, CardHeader, CardTitle, CardContent, Modal } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { Wallet, ArrowUpRight, ArrowDownLeft, Clock, CheckCircle, XCircle, Copy } from 'lucide-react';\nimport { formatCurrency, formatDateTime, copyToClipboard } from '@/lib/utils';\n\ninterface WalletData {\n  balance: number;\n  pendingEarnings: number;\n  recentTransactions: Array<{\n    id: string;\n    type: string;\n    amount: number;\n    description: string;\n    status: string;\n    createdAt: string;\n  }>;\n}\n\ninterface WithdrawalHistory {\n  id: string;\n  amount: number;\n  usdtAddress: string;\n  status: string;\n  txid?: string;\n  createdAt: string;\n  processedAt?: string;\n  rejectionReason?: string;\n}\n\nexport const WalletDashboard: React.FC = () => {\n  const [walletData, setWalletData] = useState<WalletData | null>(null);\n  const [withdrawalHistory, setWithdrawalHistory] = useState<WithdrawalHistory[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [showWithdrawModal, setShowWithdrawModal] = useState(false);\n  const [withdrawalForm, setWithdrawalForm] = useState({\n    amount: '',\n    usdtAddress: '',\n  });\n  const [withdrawalLoading, setWithdrawalLoading] = useState(false);\n  const [withdrawalError, setWithdrawalError] = useState('');\n\n  useEffect(() => {\n    fetchWalletData();\n    fetchWithdrawalHistory();\n  }, []);\n\n  const fetchWalletData = async () => {\n    try {\n      const response = await fetch('/api/wallet/balance', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWalletData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch wallet data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const fetchWithdrawalHistory = async () => {\n    try {\n      const response = await fetch('/api/wallet/withdraw', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setWithdrawalHistory(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch withdrawal history:', error);\n    }\n  };\n\n  const handleWithdrawal = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setWithdrawalError('');\n    setWithdrawalLoading(true);\n\n    try {\n      const amount = parseFloat(withdrawalForm.amount);\n      \n      if (!amount || amount <= 0) {\n        throw new Error('Please enter a valid amount');\n      }\n\n      if (!withdrawalForm.usdtAddress) {\n        throw new Error('Please enter a USDT address');\n      }\n\n      const response = await fetch('/api/wallet/withdraw', {\n        method: 'POST',\n        headers: {\n          'Content-Type': 'application/json',\n        },\n        credentials: 'include',\n        body: JSON.stringify({\n          amount,\n          usdtAddress: withdrawalForm.usdtAddress,\n        }),\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Withdrawal failed');\n      }\n\n      // Reset form and close modal\n      setWithdrawalForm({ amount: '', usdtAddress: '' });\n      setShowWithdrawModal(false);\n      \n      // Refresh data\n      fetchWalletData();\n      fetchWithdrawalHistory();\n\n    } catch (err: any) {\n      setWithdrawalError(err.message || 'Withdrawal failed');\n    } finally {\n      setWithdrawalLoading(false);\n    }\n  };\n\n  const getTransactionIcon = (type: string) => {\n    switch (type) {\n      case 'WITHDRAWAL':\n        return <ArrowUpRight className=\"h-4 w-4 text-red-500\" />;\n      case 'DEPOSIT':\n      case 'MINING_EARNINGS':\n      case 'DIRECT_REFERRAL':\n      case 'BINARY_BONUS':\n        return <ArrowDownLeft className=\"h-4 w-4 text-eco-500\" />;\n      default:\n        return <Wallet className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const getTransactionColor = (type: string) => {\n    switch (type) {\n      case 'WITHDRAWAL':\n      case 'PURCHASE':\n        return 'text-red-600';\n      case 'DEPOSIT':\n      case 'MINING_EARNINGS':\n      case 'DIRECT_REFERRAL':\n      case 'BINARY_BONUS':\n        return 'text-eco-600';\n      default:\n        return 'text-gray-600';\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'COMPLETED':\n      case 'APPROVED':\n        return <CheckCircle className=\"h-4 w-4 text-eco-500\" />;\n      case 'PENDING':\n        return <Clock className=\"h-4 w-4 text-solar-500\" />;\n      case 'FAILED':\n      case 'REJECTED':\n        return <XCircle className=\"h-4 w-4 text-red-500\" />;\n      default:\n        return <Clock className=\"h-4 w-4 text-gray-500\" />;\n    }\n  };\n\n  const handleCopyAddress = async (address: string) => {\n    try {\n      await copyToClipboard(address);\n      // You could add a toast notification here\n    } catch (error) {\n      console.error('Failed to copy address:', error);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-32 bg-gray-200 rounded-xl\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!walletData) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load wallet data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Wallet Overview */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Wallet Overview</h2>\n        <Grid cols={{ default: 1, lg: 2 }} gap={8}>\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Available Balance</p>\n                  <p className=\"text-4xl font-bold text-dark-900\">\n                    {formatCurrency(walletData.balance)}\n                  </p>\n                </div>\n                <div className=\"h-16 w-16 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <Wallet className=\"h-8 w-8 text-eco-600\" />\n                </div>\n              </div>\n              <Button\n                onClick={() => setShowWithdrawModal(true)}\n                className=\"w-full h-12 text-base font-semibold rounded-xl\"\n                disabled={walletData.balance < 10}\n              >\n                <ArrowUpRight className=\"h-5 w-5 mr-2\" />\n                Withdraw USDT\n              </Button>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-8\">\n              <div className=\"flex items-center justify-between mb-6\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Pending Earnings</p>\n                  <p className=\"text-4xl font-bold text-solar-600\">\n                    {formatCurrency(walletData.pendingEarnings)}\n                  </p>\n                </div>\n                <div className=\"h-16 w-16 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Clock className=\"h-8 w-8 text-solar-600\" />\n                </div>\n              </div>\n              <p className=\"text-sm text-gray-600 font-medium\">\n                Will be transferred on Saturday at 15:00 UTC\n              </p>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      {/* Recent Transactions */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Recent Transactions</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {walletData.recentTransactions.length > 0 ? (\n            <div className=\"space-y-3\">\n              {walletData.recentTransactions.map((transaction) => (\n                <div key={transaction.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex items-center space-x-3\">\n                    {getTransactionIcon(transaction.type)}\n                    <div>\n                      <p className=\"font-medium text-dark-900\">{transaction.description}</p>\n                      <p className=\"text-sm text-gray-500\">{formatDateTime(transaction.createdAt)}</p>\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    {getStatusIcon(transaction.status)}\n                    <span className={`font-semibold ${getTransactionColor(transaction.type)}`}>\n                      {transaction.type === 'WITHDRAWAL' || transaction.type === 'PURCHASE' ? '-' : '+'}\n                      {formatCurrency(transaction.amount)}\n                    </span>\n                  </div>\n                </div>\n              ))}\n            </div>\n          ) : (\n            <div className=\"text-center py-8\">\n              <p className=\"text-gray-500\">No transactions yet</p>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Withdrawal History */}\n      {withdrawalHistory.length > 0 && (\n        <Card>\n          <CardHeader>\n            <CardTitle>Withdrawal History</CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              {withdrawalHistory.map((withdrawal) => (\n                <div key={withdrawal.id} className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\">\n                  <div className=\"flex-1\">\n                    <div className=\"flex items-center space-x-2\">\n                      <span className=\"font-medium text-dark-900\">\n                        {formatCurrency(withdrawal.amount)}\n                      </span>\n                      {getStatusIcon(withdrawal.status)}\n                      <span className={`text-sm px-2 py-1 rounded-full ${\n                        withdrawal.status === 'COMPLETED' ? 'bg-eco-100 text-eco-700' :\n                        withdrawal.status === 'PENDING' ? 'bg-solar-100 text-solar-700' :\n                        'bg-red-100 text-red-700'\n                      }`}>\n                        {withdrawal.status}\n                      </span>\n                    </div>\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <p className=\"text-sm text-gray-600\">\n                        To: {withdrawal.usdtAddress.slice(0, 8)}...{withdrawal.usdtAddress.slice(-8)}\n                      </p>\n                      <button\n                        onClick={() => handleCopyAddress(withdrawal.usdtAddress)}\n                        className=\"text-gray-400 hover:text-gray-600\"\n                      >\n                        <Copy className=\"h-3 w-3\" />\n                      </button>\n                    </div>\n                    <p className=\"text-xs text-gray-500\">{formatDateTime(withdrawal.createdAt)}</p>\n                    {withdrawal.txid && (\n                      <p className=\"text-xs text-eco-600 mt-1\">\n                        TXID: {withdrawal.txid.slice(0, 16)}...\n                      </p>\n                    )}\n                    {withdrawal.rejectionReason && (\n                      <p className=\"text-xs text-red-600 mt-1\">\n                        Reason: {withdrawal.rejectionReason}\n                      </p>\n                    )}\n                  </div>\n                </div>\n              ))}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Withdrawal Modal */}\n      <Modal\n        isOpen={showWithdrawModal}\n        onClose={() => setShowWithdrawModal(false)}\n        title=\"Withdraw USDT\"\n      >\n        <form onSubmit={handleWithdrawal} className=\"space-y-4\">\n          {withdrawalError && (\n            <div className=\"bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n              {withdrawalError}\n            </div>\n          )}\n\n          <div className=\"bg-blue-50 border border-blue-200 text-blue-700 px-4 py-3 rounded-lg text-sm\">\n            <p><strong>Available Balance:</strong> {formatCurrency(walletData.balance)}</p>\n            <p><strong>Minimum Withdrawal:</strong> $10.00</p>\n            <p><strong>Network:</strong> USDT (TRC20)</p>\n          </div>\n\n          <Input\n            label=\"Withdrawal Amount (USD)\"\n            type=\"number\"\n            step=\"0.01\"\n            min=\"10\"\n            max={walletData.balance}\n            value={withdrawalForm.amount}\n            onChange={(e) => setWithdrawalForm(prev => ({ ...prev, amount: e.target.value }))}\n            placeholder=\"Enter amount to withdraw\"\n            required\n          />\n\n          <Input\n            label=\"USDT TRC20 Address\"\n            type=\"text\"\n            value={withdrawalForm.usdtAddress}\n            onChange={(e) => setWithdrawalForm(prev => ({ ...prev, usdtAddress: e.target.value }))}\n            placeholder=\"Enter your USDT TRC20 address\"\n            required\n          />\n\n          <div className=\"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded-lg text-sm\">\n            <p><strong>Important:</strong></p>\n            <ul className=\"list-disc list-inside mt-1 space-y-1\">\n              <li>Only USDT TRC20 addresses are supported</li>\n              <li>Withdrawals require KYC verification</li>\n              <li>Processing time: 1-3 business days</li>\n              <li>Double-check your address - transactions cannot be reversed</li>\n            </ul>\n          </div>\n\n          <div className=\"flex space-x-3\">\n            <Button\n              type=\"button\"\n              variant=\"outline\"\n              onClick={() => setShowWithdrawModal(false)}\n              className=\"flex-1\"\n            >\n              Cancel\n            </Button>\n            <Button\n              type=\"submit\"\n              loading={withdrawalLoading}\n              className=\"flex-1\"\n            >\n              Submit Withdrawal\n            </Button>\n          </div>\n        </form>\n      </Modal>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AAgCO,MAAM,kBAA4B;;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAqB;IAChE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAuB,EAAE;IAClF,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;QACnD,QAAQ;QACR,aAAa;IACf;IACA,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;qCAAE;YACR;YACA;QACF;oCAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,uBAAuB;gBAClD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,cAAc,KAAK,IAAI;gBACzB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,gCAAgC;QAChD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,yBAAyB;QAC7B,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,qBAAqB,KAAK,IAAI;gBAChC;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uCAAuC;QACvD;IACF;IAEA,MAAM,mBAAmB,OAAO;QAC9B,EAAE,cAAc;QAChB,mBAAmB;QACnB,qBAAqB;QAErB,IAAI;YACF,MAAM,SAAS,WAAW,eAAe,MAAM;YAE/C,IAAI,CAAC,UAAU,UAAU,GAAG;gBAC1B,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,CAAC,eAAe,WAAW,EAAE;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,MAAM,MAAM,wBAAwB;gBACnD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,aAAa;gBACb,MAAM,KAAK,SAAS,CAAC;oBACnB;oBACA,aAAa,eAAe,WAAW;gBACzC;YACF;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,6BAA6B;YAC7B,kBAAkB;gBAAE,QAAQ;gBAAI,aAAa;YAAG;YAChD,qBAAqB;YAErB,eAAe;YACf;YACA;QAEF,EAAE,OAAO,KAAU;YACjB,mBAAmB,IAAI,OAAO,IAAI;QACpC,SAAU;YACR,qBAAqB;QACvB;IACF;IAEA,MAAM,qBAAqB,CAAC;QAC1B,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,6NAAA,CAAA,eAAY;oBAAC,WAAU;;;;;;YACjC,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,+NAAA,CAAA,gBAAa;oBAAC,WAAU;;;;;;YAClC;gBACE,qBAAO,6LAAC,yMAAA,CAAA,SAAM;oBAAC,WAAU;;;;;;QAC7B;IACF;IAEA,MAAM,sBAAsB,CAAC;QAC3B,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;YACT,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B,KAAK;YACL,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B;gBACE,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;QAC5B;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACtB,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,2BAA2B;QAC3C;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,YAAY;QACf,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CACtC,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyC;;;;;;sEACtD,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO;;;;;;;;;;;;8DAGtC,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,yMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGtB,6LAAC,qIAAA,CAAA,SAAM;4CACL,SAAS,IAAM,qBAAqB;4CACpC,WAAU;4CACV,UAAU,WAAW,OAAO,GAAG;;8DAE/B,6LAAC,6NAAA,CAAA,eAAY;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;;;;;;0CAM/C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAE,WAAU;sEAAyC;;;;;;sEACtD,6LAAC;4DAAE,WAAU;sEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,eAAe;;;;;;;;;;;;8DAG9C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;wDAAC,WAAU;;;;;;;;;;;;;;;;;sDAGrB,6LAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASzD,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACT,WAAW,kBAAkB,CAAC,MAAM,GAAG,kBACtC,6LAAC;4BAAI,WAAU;sCACZ,WAAW,kBAAkB,CAAC,GAAG,CAAC,CAAC,4BAClC,6LAAC;oCAAyB,WAAU;;sDAClC,6LAAC;4CAAI,WAAU;;gDACZ,mBAAmB,YAAY,IAAI;8DACpC,6LAAC;;sEACC,6LAAC;4DAAE,WAAU;sEAA6B,YAAY,WAAW;;;;;;sEACjE,6LAAC;4DAAE,WAAU;sEAAyB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,SAAS;;;;;;;;;;;;;;;;;;sDAG9E,6LAAC;4CAAI,WAAU;;gDACZ,cAAc,YAAY,MAAM;8DACjC,6LAAC;oDAAK,WAAW,CAAC,cAAc,EAAE,oBAAoB,YAAY,IAAI,GAAG;;wDACtE,YAAY,IAAI,KAAK,gBAAgB,YAAY,IAAI,KAAK,aAAa,MAAM;wDAC7E,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM;;;;;;;;;;;;;;mCAZ9B,YAAY,EAAE;;;;;;;;;iDAmB5B,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAE,WAAU;0CAAgB;;;;;;;;;;;;;;;;;;;;;;YAOpC,kBAAkB,MAAM,GAAG,mBAC1B,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;kCACV,cAAA,6LAAC;4BAAI,WAAU;sCACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,6LAAC;oCAAwB,WAAU;8CACjC,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,MAAM;;;;;;oDAElC,cAAc,WAAW,MAAM;kEAChC,6LAAC;wDAAK,WAAW,CAAC,+BAA+B,EAC/C,WAAW,MAAM,KAAK,cAAc,4BACpC,WAAW,MAAM,KAAK,YAAY,gCAClC,2BACA;kEACC,WAAW,MAAM;;;;;;;;;;;;0DAGtB,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;;4DAAwB;4DAC9B,WAAW,WAAW,CAAC,KAAK,CAAC,GAAG;4DAAG;4DAAI,WAAW,WAAW,CAAC,KAAK,CAAC,CAAC;;;;;;;kEAE5E,6LAAC;wDACC,SAAS,IAAM,kBAAkB,WAAW,WAAW;wDACvD,WAAU;kEAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;;;;;;;;;;;;0DAGpB,6LAAC;gDAAE,WAAU;0DAAyB,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,SAAS;;;;;;4CACxE,WAAW,IAAI,kBACd,6LAAC;gDAAE,WAAU;;oDAA4B;oDAChC,WAAW,IAAI,CAAC,KAAK,CAAC,GAAG;oDAAI;;;;;;;4CAGvC,WAAW,eAAe,kBACzB,6LAAC;gDAAE,WAAU;;oDAA4B;oDAC9B,WAAW,eAAe;;;;;;;;;;;;;mCAlCjC,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;0BA8CjC,6LAAC,oIAAA,CAAA,QAAK;gBACJ,QAAQ;gBACR,SAAS,IAAM,qBAAqB;gBACpC,OAAM;0BAEN,cAAA,6LAAC;oBAAK,UAAU;oBAAkB,WAAU;;wBACzC,iCACC,6LAAC;4BAAI,WAAU;sCACZ;;;;;;sCAIL,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAA2B;wCAAE,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,WAAW,OAAO;;;;;;;8CACzE,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAA4B;;;;;;;8CACvC,6LAAC;;sDAAE,6LAAC;sDAAO;;;;;;wCAAiB;;;;;;;;;;;;;sCAG9B,6LAAC,oIAAA,CAAA,QAAK;4BACJ,OAAM;4BACN,MAAK;4BACL,MAAK;4BACL,KAAI;4BACJ,KAAK,WAAW,OAAO;4BACvB,OAAO,eAAe,MAAM;4BAC5B,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;oCAAC,CAAC;4BAC/E,aAAY;4BACZ,QAAQ;;;;;;sCAGV,6LAAC,oIAAA,CAAA,QAAK;4BACJ,OAAM;4BACN,MAAK;4BACL,OAAO,eAAe,WAAW;4BACjC,UAAU,CAAC,IAAM,kBAAkB,CAAA,OAAQ,CAAC;wCAAE,GAAG,IAAI;wCAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oCAAC,CAAC;4BACpF,aAAY;4BACZ,QAAQ;;;;;;sCAGV,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;8CAAE,cAAA,6LAAC;kDAAO;;;;;;;;;;;8CACX,6LAAC;oCAAG,WAAU;;sDACZ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;sDACJ,6LAAC;sDAAG;;;;;;;;;;;;;;;;;;sCAIR,6LAAC;4BAAI,WAAU;;8CACb,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAQ;oCACR,SAAS,IAAM,qBAAqB;oCACpC,WAAU;8CACX;;;;;;8CAGD,6LAAC,qIAAA,CAAA,SAAM;oCACL,MAAK;oCACL,SAAS;oCACT,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb;GAlYa;KAAA", "debugId": null}}, {"offset": {"line": 6215, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/BinaryTreeVisualizer.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, Card<PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardContent, Button } from '@/components/ui';\nimport { Grid } from '@/components/layout';\nimport { Users, Copy, Share2, TrendingUp, Award } from 'lucide-react';\nimport { formatCurrency, formatDate, copyToClipboard, getTimeUntilBinaryPayout } from '@/lib/utils';\n\ninterface BinaryTreeNode {\n  user: {\n    id: string;\n    email: string;\n    createdAt: string;\n  };\n  binaryPoints: {\n    leftPoints: number;\n    rightPoints: number;\n    matchedPoints: number;\n  };\n  leftChild?: BinaryTreeNode;\n  rightChild?: BinaryTreeNode;\n}\n\ninterface BinaryTreeData {\n  treeStructure: BinaryTreeNode;\n  statistics: {\n    totalDirectReferrals: number;\n    leftReferrals: number;\n    rightReferrals: number;\n    totalCommissions: number;\n    binaryPoints: {\n      leftPoints: number;\n      rightPoints: number;\n      matchedPoints: number;\n    };\n  };\n  referralLinks: {\n    left: string;\n    right: string;\n    general: string;\n  };\n}\n\nexport const BinaryTreeVisualizer: React.FC = () => {\n  const [treeData, setTreeData] = useState<BinaryTreeData | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [timeUntilMatching, setTimeUntilMatching] = useState(getTimeUntilBinaryPayout());\n\n  useEffect(() => {\n    fetchTreeData();\n    \n    // Update countdown every second\n    const interval = setInterval(() => {\n      setTimeUntilMatching(getTimeUntilBinaryPayout());\n    }, 1000);\n\n    return () => clearInterval(interval);\n  }, []);\n\n  const fetchTreeData = async () => {\n    try {\n      const response = await fetch('/api/referrals/tree?depth=3', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setTreeData(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch binary tree data:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleCopyLink = async (link: string) => {\n    try {\n      await copyToClipboard(link);\n      // You could add a toast notification here\n    } catch (error) {\n      console.error('Failed to copy link:', error);\n    }\n  };\n\n  const TreeNodeComponent: React.FC<{ node: BinaryTreeNode | null; position: string }> = ({ node, position }) => {\n    if (!node) {\n      return (\n        <div className=\"flex flex-col items-center\">\n          <div className=\"w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center\">\n            <span className=\"text-gray-400 text-xs\">Empty</span>\n          </div>\n          <p className=\"text-xs text-gray-500 mt-2\">{position}</p>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"flex flex-col items-center\">\n        <div className=\"w-24 h-24 bg-eco-100 border-2 border-eco-300 rounded-lg flex flex-col items-center justify-center p-2\">\n          <div className=\"text-xs font-semibold text-eco-700 truncate w-full text-center\">\n            {node.user.email.split('@')[0]}\n          </div>\n          <div className=\"text-xs text-gray-600 mt-1\">\n            {formatDate(node.user.createdAt)}\n          </div>\n          <div className=\"text-xs text-eco-600 font-medium\">\n            L: {node.binaryPoints.leftPoints} | R: {node.binaryPoints.rightPoints}\n          </div>\n        </div>\n        <p className=\"text-xs text-gray-500 mt-2\">{position}</p>\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-64 bg-gray-200 rounded-xl\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  if (!treeData) {\n    return (\n      <Card>\n        <CardContent className=\"text-center py-8\">\n          <p className=\"text-gray-500\">Failed to load binary tree data</p>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Binary Statistics */}\n      <div>\n        <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">Referral Network</h2>\n        <Grid cols={{ default: 1, sm: 2, lg: 4 }} gap={6}>\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Direct Referrals</p>\n                  <p className=\"text-3xl font-bold text-dark-900\">\n                    {treeData.statistics.totalDirectReferrals}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-blue-100 rounded-xl flex items-center justify-center\">\n                  <Users className=\"h-7 w-7 text-blue-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Total Commissions</p>\n                  <p className=\"text-3xl font-bold text-eco-600\">\n                    {formatCurrency(treeData.statistics.totalCommissions)}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-eco-100 rounded-xl flex items-center justify-center\">\n                  <TrendingUp className=\"h-7 w-7 text-eco-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Left Points</p>\n                  <p className=\"text-3xl font-bold text-solar-600\">\n                    {treeData.statistics.binaryPoints.leftPoints}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Award className=\"h-7 w-7 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          <Card className=\"hover:shadow-lg transition-shadow duration-200\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div className=\"flex-1\">\n                  <p className=\"text-sm font-medium text-gray-600 mb-2\">Right Points</p>\n                  <p className=\"text-3xl font-bold text-solar-600\">\n                    {treeData.statistics.binaryPoints.rightPoints}\n                  </p>\n                </div>\n                <div className=\"h-14 w-14 bg-solar-100 rounded-xl flex items-center justify-center\">\n                  <Award className=\"h-7 w-7 text-solar-600\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Grid>\n      </div>\n\n      <Grid cols={{ default: 1, lg: 2 }} gap={6}>\n        {/* Binary Tree Visualization */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"flex items-center space-x-2\">\n              <Users className=\"h-5 w-5 text-blue-500\" />\n              <span>Binary Tree Structure</span>\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"flex flex-col items-center space-y-8\">\n              {/* Root (You) */}\n              <TreeNodeComponent node={treeData.treeStructure} position=\"You\" />\n              \n              {/* Level 1 */}\n              <div className=\"flex justify-center space-x-16\">\n                <TreeNodeComponent \n                  node={treeData.treeStructure.leftChild || null} \n                  position=\"Left\" \n                />\n                <TreeNodeComponent \n                  node={treeData.treeStructure.rightChild || null} \n                  position=\"Right\" \n                />\n              </div>\n              \n              {/* Level 2 */}\n              <div className=\"flex justify-center space-x-8\">\n                <div className=\"flex space-x-4\">\n                  <TreeNodeComponent \n                    node={treeData.treeStructure.leftChild?.leftChild || null} \n                    position=\"L-L\" \n                  />\n                  <TreeNodeComponent \n                    node={treeData.treeStructure.leftChild?.rightChild || null} \n                    position=\"L-R\" \n                  />\n                </div>\n                <div className=\"flex space-x-4\">\n                  <TreeNodeComponent \n                    node={treeData.treeStructure.rightChild?.leftChild || null} \n                    position=\"R-L\" \n                  />\n                  <TreeNodeComponent \n                    node={treeData.treeStructure.rightChild?.rightChild || null} \n                    position=\"R-R\" \n                  />\n                </div>\n              </div>\n            </div>\n            \n            <div className=\"mt-6 p-4 bg-gray-50 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-gray-700 mb-2\">Legend:</h4>\n              <div className=\"text-xs text-gray-600 space-y-1\">\n                <p>• L: Left side points | R: Right side points</p>\n                <p>• Points are added when downline users make investments</p>\n                <p>• Binary matching occurs daily at 12:00 AM UTC</p>\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Next Binary Matching & Referral Links */}\n        <div className=\"space-y-6\">\n          {/* Next Binary Matching */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Award className=\"h-5 w-5 text-solar-500\" />\n                <span>Next Binary Matching</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"text-center\">\n                <p className=\"text-sm text-gray-600 mb-4\">\n                  Daily binary matching at 12:00 AM UTC\n                </p>\n                <div className=\"grid grid-cols-3 gap-4\">\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-solar-600\">\n                      {timeUntilMatching.hours}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">Hours</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-solar-600\">\n                      {timeUntilMatching.minutes}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">Minutes</div>\n                  </div>\n                  <div className=\"text-center\">\n                    <div className=\"text-2xl font-bold text-solar-600\">\n                      {timeUntilMatching.seconds}\n                    </div>\n                    <div className=\"text-xs text-gray-500\">Seconds</div>\n                  </div>\n                </div>\n                \n                <div className=\"mt-4 p-3 bg-solar-50 rounded-lg\">\n                  <p className=\"text-sm text-solar-700\">\n                    <strong>Potential Match:</strong> {Math.min(treeData.statistics.binaryPoints.leftPoints, treeData.statistics.binaryPoints.rightPoints)} points\n                  </p>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Referral Links */}\n          <Card>\n            <CardHeader>\n              <CardTitle className=\"flex items-center space-x-2\">\n                <Share2 className=\"h-5 w-5 text-eco-500\" />\n                <span>Referral Links</span>\n              </CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"space-y-4\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">General Referral Link</label>\n                  <div className=\"flex mt-1\">\n                    <input\n                      type=\"text\"\n                      value={treeData.referralLinks.general}\n                      readOnly\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                    />\n                    <Button\n                      size=\"sm\"\n                      onClick={() => handleCopyLink(treeData.referralLinks.general)}\n                      className=\"rounded-l-none\"\n                    >\n                      <Copy className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">Left Side Link</label>\n                  <div className=\"flex mt-1\">\n                    <input\n                      type=\"text\"\n                      value={treeData.referralLinks.left}\n                      readOnly\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                    />\n                    <Button\n                      size=\"sm\"\n                      onClick={() => handleCopyLink(treeData.referralLinks.left)}\n                      className=\"rounded-l-none\"\n                    >\n                      <Copy className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"text-sm font-medium text-gray-700\">Right Side Link</label>\n                  <div className=\"flex mt-1\">\n                    <input\n                      type=\"text\"\n                      value={treeData.referralLinks.right}\n                      readOnly\n                      className=\"flex-1 px-3 py-2 border border-gray-300 rounded-l-lg text-sm bg-gray-50\"\n                    />\n                    <Button\n                      size=\"sm\"\n                      onClick={() => handleCopyLink(treeData.referralLinks.right)}\n                      className=\"rounded-l-none\"\n                    >\n                      <Copy className=\"h-4 w-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </div>\n\n              <div className=\"mt-4 p-3 bg-blue-50 rounded-lg\">\n                <p className=\"text-xs text-blue-700\">\n                  <strong>Tip:</strong> Share your referral links to build your binary network. \n                  New users will be placed automatically in your weaker leg.\n                </p>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </Grid>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;;;AANA;;;;;;AA2CO,MAAM,uBAAiC;;IAC5C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAyB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;IAElF,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;0CAAE;YACR;YAEA,gCAAgC;YAChC,MAAM,WAAW;2DAAY;oBAC3B,qBAAqB,CAAA,GAAA,sHAAA,CAAA,2BAAwB,AAAD;gBAC9C;0DAAG;YAEH;kDAAO,IAAM,cAAc;;QAC7B;yCAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,+BAA+B;gBAC1D,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,YAAY,KAAK,IAAI;gBACvB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;QACrD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,iBAAiB,OAAO;QAC5B,IAAI;YACF,MAAM,CAAA,GAAA,sHAAA,CAAA,kBAAe,AAAD,EAAE;QACtB,0CAA0C;QAC5C,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,wBAAwB;QACxC;IACF;IAEA,MAAM,oBAAiF,CAAC,EAAE,IAAI,EAAE,QAAQ,EAAE;QACxG,IAAI,CAAC,MAAM;YACT,qBACE,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAK,WAAU;sCAAwB;;;;;;;;;;;kCAE1C,6LAAC;wBAAE,WAAU;kCAA8B;;;;;;;;;;;;QAGjD;QAEA,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;sCACZ,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,EAAE;;;;;;sCAEhC,6LAAC;4BAAI,WAAU;sCACZ,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,IAAI,CAAC,SAAS;;;;;;sCAEjC,6LAAC;4BAAI,WAAU;;gCAAmC;gCAC5C,KAAK,YAAY,CAAC,UAAU;gCAAC;gCAAO,KAAK,YAAY,CAAC,WAAW;;;;;;;;;;;;;8BAGzE,6LAAC;oBAAE,WAAU;8BAA8B;;;;;;;;;;;;IAGjD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;;;;;;;;;;;;;;;IAIvB;IAEA,IAAI,CAAC,UAAU;QACb,qBACE,6LAAC,mIAAA,CAAA,OAAI;sBACH,cAAA,6LAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;0BACrB,cAAA,6LAAC;oBAAE,WAAU;8BAAgB;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC;;kCACC,6LAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6LAAC,uIAAA,CAAA,OAAI;wBAAC,MAAM;4BAAE,SAAS;4BAAG,IAAI;4BAAG,IAAI;wBAAE;wBAAG,KAAK;;0CAC7C,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,SAAS,UAAU,CAAC,oBAAoB;;;;;;;;;;;;0DAG7C,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,SAAS,UAAU,CAAC,gBAAgB;;;;;;;;;;;;0DAGxD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,qNAAA,CAAA,aAAU;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAM9B,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,SAAS,UAAU,CAAC,YAAY,CAAC,UAAU;;;;;;;;;;;;0DAGhD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAMzB,6LAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;0CACd,cAAA,6LAAC,mIAAA,CAAA,cAAW;oCAAC,WAAU;8CACrB,cAAA,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAE,WAAU;kEAAyC;;;;;;kEACtD,6LAAC;wDAAE,WAAU;kEACV,SAAS,UAAU,CAAC,YAAY,CAAC,WAAW;;;;;;;;;;;;0DAGjD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQ7B,6LAAC,uIAAA,CAAA,OAAI;gBAAC,MAAM;oBAAE,SAAS;oBAAG,IAAI;gBAAE;gBAAG,KAAK;;kCAEtC,6LAAC,mIAAA,CAAA,OAAI;;0CACH,6LAAC,mIAAA,CAAA,aAAU;0CACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oCAAC,WAAU;;sDACnB,6LAAC,uMAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;sDACjB,6LAAC;sDAAK;;;;;;;;;;;;;;;;;0CAGV,6LAAC,mIAAA,CAAA,cAAW;;kDACV,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAkB,MAAM,SAAS,aAAa;gDAAE,UAAS;;;;;;0DAG1D,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAM,SAAS,aAAa,CAAC,SAAS,IAAI;wDAC1C,UAAS;;;;;;kEAEX,6LAAC;wDACC,MAAM,SAAS,aAAa,CAAC,UAAU,IAAI;wDAC3C,UAAS;;;;;;;;;;;;0DAKb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAM,SAAS,aAAa,CAAC,SAAS,EAAE,aAAa;gEACrD,UAAS;;;;;;0EAEX,6LAAC;gEACC,MAAM,SAAS,aAAa,CAAC,SAAS,EAAE,cAAc;gEACtD,UAAS;;;;;;;;;;;;kEAGb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEACC,MAAM,SAAS,aAAa,CAAC,UAAU,EAAE,aAAa;gEACtD,UAAS;;;;;;0EAEX,6LAAC;gEACC,MAAM,SAAS,aAAa,CAAC,UAAU,EAAE,cAAc;gEACvD,UAAS;;;;;;;;;;;;;;;;;;;;;;;;kDAMjB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAyC;;;;;;0DACvD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;kEACH,6LAAC;kEAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOX,6LAAC;wBAAI,WAAU;;0CAEb,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,uMAAA,CAAA,QAAK;oDAAC,WAAU;;;;;;8DACjB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;kDACV,cAAA,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAE,WAAU;8DAA6B;;;;;;8DAG1C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,kBAAkB,KAAK;;;;;;8EAE1B,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,kBAAkB,OAAO;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;sEAEzC,6LAAC;4DAAI,WAAU;;8EACb,6LAAC;oEAAI,WAAU;8EACZ,kBAAkB,OAAO;;;;;;8EAE5B,6LAAC;oEAAI,WAAU;8EAAwB;;;;;;;;;;;;;;;;;;8DAI3C,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAE,WAAU;;0EACX,6LAAC;0EAAO;;;;;;4DAAyB;4DAAE,KAAK,GAAG,CAAC,SAAS,UAAU,CAAC,YAAY,CAAC,UAAU,EAAE,SAAS,UAAU,CAAC,YAAY,CAAC,WAAW;4DAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAQjJ,6LAAC,mIAAA,CAAA,OAAI;;kDACH,6LAAC,mIAAA,CAAA,aAAU;kDACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4CAAC,WAAU;;8DACnB,6LAAC,6MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,6LAAC;8DAAK;;;;;;;;;;;;;;;;;kDAGV,6LAAC,mIAAA,CAAA,cAAW;;0DACV,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,OAAO,SAAS,aAAa,CAAC,OAAO;wEACrC,QAAQ;wEACR,WAAU;;;;;;kFAEZ,6LAAC,qIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAS,IAAM,eAAe,SAAS,aAAa,CAAC,OAAO;wEAC5D,WAAU;kFAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKtB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,OAAO,SAAS,aAAa,CAAC,IAAI;wEAClC,QAAQ;wEACR,WAAU;;;;;;kFAEZ,6LAAC,qIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAS,IAAM,eAAe,SAAS,aAAa,CAAC,IAAI;wEACzD,WAAU;kFAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;kEAKtB,6LAAC;;0EACC,6LAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEACC,MAAK;wEACL,OAAO,SAAS,aAAa,CAAC,KAAK;wEACnC,QAAQ;wEACR,WAAU;;;;;;kFAEZ,6LAAC,qIAAA,CAAA,SAAM;wEACL,MAAK;wEACL,SAAS,IAAM,eAAe,SAAS,aAAa,CAAC,KAAK;wEAC1D,WAAU;kFAEV,cAAA,6LAAC,qMAAA,CAAA,OAAI;4EAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMxB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAE,WAAU;;sEACX,6LAAC;sEAAO;;;;;;wDAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUvC;GAjWa;KAAA", "debugId": null}}, {"offset": {"line": 7337, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/KYCPortal.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';\nimport { useAuth } from '@/hooks/useAuth';\nimport { Shield, Upload, CheckCircle, XCircle, Clock, AlertCircle } from 'lucide-react';\n\ninterface KYCDocument {\n  id: string;\n  documentType: 'ID' | 'SELFIE';\n  filePath: string;\n  status: 'PENDING' | 'APPROVED' | 'REJECTED';\n  reviewedAt?: string;\n  rejectionReason?: string;\n  createdAt: string;\n}\n\nexport const KYCPortal: React.FC = () => {\n  const { user, refreshUser } = useAuth();\n  const [documents, setDocuments] = useState<KYCDocument[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [uploading, setUploading] = useState(false);\n  const [uploadError, setUploadError] = useState('');\n\n  useEffect(() => {\n    fetchKYCDocuments();\n  }, []);\n\n  const fetchKYCDocuments = async () => {\n    try {\n      const response = await fetch('/api/kyc/documents', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setDocuments(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch KYC documents:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleFileUpload = async (documentType: 'ID' | 'SELFIE', file: File) => {\n    setUploading(true);\n    setUploadError('');\n\n    try {\n      // Validate file\n      if (!file.type.startsWith('image/')) {\n        throw new Error('Please upload an image file');\n      }\n\n      if (file.size > 5 * 1024 * 1024) { // 5MB limit\n        throw new Error('File size must be less than 5MB');\n      }\n\n      const formData = new FormData();\n      formData.append('file', file);\n      formData.append('documentType', documentType);\n\n      const response = await fetch('/api/kyc/upload', {\n        method: 'POST',\n        credentials: 'include',\n        body: formData,\n      });\n\n      const data = await response.json();\n\n      if (!data.success) {\n        throw new Error(data.error || 'Upload failed');\n      }\n\n      // Refresh documents and user data\n      await fetchKYCDocuments();\n      await refreshUser();\n\n    } catch (err: any) {\n      setUploadError(err.message || 'Upload failed');\n    } finally {\n      setUploading(false);\n    }\n  };\n\n  const getStatusIcon = (status: string) => {\n    switch (status) {\n      case 'APPROVED':\n        return <CheckCircle className=\"h-5 w-5 text-eco-500\" />;\n      case 'REJECTED':\n        return <XCircle className=\"h-5 w-5 text-red-500\" />;\n      case 'PENDING':\n        return <Clock className=\"h-5 w-5 text-solar-500\" />;\n      default:\n        return <AlertCircle className=\"h-5 w-5 text-gray-500\" />;\n    }\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'APPROVED':\n        return 'bg-eco-100 text-eco-700';\n      case 'REJECTED':\n        return 'bg-red-100 text-red-700';\n      case 'PENDING':\n        return 'bg-solar-100 text-solar-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  const getDocumentByType = (type: 'ID' | 'SELFIE') => {\n    return documents.find(doc => doc.documentType === type);\n  };\n\n  const FileUploadSection: React.FC<{ \n    documentType: 'ID' | 'SELFIE'; \n    title: string; \n    description: string;\n  }> = ({ documentType, title, description }) => {\n    const existingDoc = getDocumentByType(documentType);\n    const inputId = `file-${documentType}`;\n\n    return (\n      <div className=\"border border-gray-200 rounded-lg p-6\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <div>\n            <h3 className=\"text-lg font-semibold text-dark-900\">{title}</h3>\n            <p className=\"text-sm text-gray-600\">{description}</p>\n          </div>\n          {existingDoc && (\n            <div className=\"flex items-center space-x-2\">\n              {getStatusIcon(existingDoc.status)}\n              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(existingDoc.status)}`}>\n                {existingDoc.status}\n              </span>\n            </div>\n          )}\n        </div>\n\n        {existingDoc ? (\n          <div className=\"space-y-3\">\n            <div className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\">\n              <img\n                src={existingDoc.filePath}\n                alt={`${documentType} document`}\n                className=\"w-16 h-16 object-cover rounded-lg\"\n              />\n              <div className=\"flex-1\">\n                <p className=\"text-sm font-medium text-dark-900\">\n                  {documentType === 'ID' ? 'ID Document' : 'Selfie Photo'}\n                </p>\n                <p className=\"text-xs text-gray-500\">\n                  Uploaded on {new Date(existingDoc.createdAt).toLocaleDateString()}\n                </p>\n                {existingDoc.rejectionReason && (\n                  <p className=\"text-xs text-red-600 mt-1\">\n                    Rejection reason: {existingDoc.rejectionReason}\n                  </p>\n                )}\n              </div>\n            </div>\n\n            {existingDoc.status === 'REJECTED' && (\n              <div>\n                <input\n                  id={inputId}\n                  type=\"file\"\n                  accept=\"image/*\"\n                  onChange={(e) => {\n                    const file = e.target.files?.[0];\n                    if (file) {\n                      handleFileUpload(documentType, file);\n                    }\n                  }}\n                  className=\"hidden\"\n                />\n                <label\n                  htmlFor={inputId}\n                  className=\"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 cursor-pointer\"\n                >\n                  <Upload className=\"h-4 w-4 mr-2\" />\n                  Re-upload Document\n                </label>\n              </div>\n            )}\n          </div>\n        ) : (\n          <div>\n            <input\n              id={inputId}\n              type=\"file\"\n              accept=\"image/*\"\n              onChange={(e) => {\n                const file = e.target.files?.[0];\n                if (file) {\n                  handleFileUpload(documentType, file);\n                }\n              }}\n              className=\"hidden\"\n            />\n            <label\n              htmlFor={inputId}\n              className=\"flex flex-col items-center justify-center w-full h-32 border-2 border-gray-300 border-dashed rounded-lg cursor-pointer bg-gray-50 hover:bg-gray-100\"\n            >\n              <div className=\"flex flex-col items-center justify-center pt-5 pb-6\">\n                <Upload className=\"h-8 w-8 text-gray-400 mb-2\" />\n                <p className=\"text-sm text-gray-500\">\n                  <span className=\"font-semibold\">Click to upload</span> or drag and drop\n                </p>\n                <p className=\"text-xs text-gray-500\">PNG, JPG up to 5MB</p>\n              </div>\n            </label>\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  if (loading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Shield className=\"h-5 w-5 text-solar-500\" />\n            <span>KYC Verification</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"animate-pulse space-y-4\">\n            <div className=\"h-32 bg-gray-200 rounded-lg\"></div>\n            <div className=\"h-32 bg-gray-200 rounded-lg\"></div>\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* KYC Status Overview */}\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <Shield className=\"h-5 w-5 text-solar-500\" />\n            <span>KYC Verification Status</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"flex items-center space-x-4\">\n            {getStatusIcon(user?.kycStatus || 'PENDING')}\n            <div>\n              <p className=\"text-lg font-semibold text-dark-900\">\n                Status: <span className={`${\n                  user?.kycStatus === 'APPROVED' ? 'text-eco-600' :\n                  user?.kycStatus === 'REJECTED' ? 'text-red-600' :\n                  'text-solar-600'\n                }`}>\n                  {user?.kycStatus || 'PENDING'}\n                </span>\n              </p>\n              <p className=\"text-sm text-gray-600\">\n                {user?.kycStatus === 'APPROVED' && 'Your identity has been verified. You can now make withdrawals.'}\n                {user?.kycStatus === 'PENDING' && 'Your documents are being reviewed. This usually takes 1-3 business days.'}\n                {user?.kycStatus === 'REJECTED' && 'Your verification was rejected. Please re-upload your documents.'}\n              </p>\n            </div>\n          </div>\n\n          {user?.kycStatus !== 'APPROVED' && (\n            <div className=\"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg\">\n              <h4 className=\"text-sm font-medium text-blue-900 mb-2\">Why do we need KYC verification?</h4>\n              <ul className=\"text-sm text-blue-700 space-y-1\">\n                <li>• Comply with international financial regulations</li>\n                <li>• Protect your account from unauthorized access</li>\n                <li>• Enable secure withdrawals to your wallet</li>\n                <li>• Prevent fraud and money laundering</li>\n              </ul>\n            </div>\n          )}\n        </CardContent>\n      </Card>\n\n      {/* Document Upload */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Upload Documents</CardTitle>\n        </CardHeader>\n        <CardContent>\n          {uploadError && (\n            <div className=\"mb-4 bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm\">\n              {uploadError}\n            </div>\n          )}\n\n          {uploading && (\n            <div className=\"mb-4 bg-blue-50 border border-blue-200 text-blue-600 px-4 py-3 rounded-lg text-sm\">\n              Uploading document... Please wait.\n            </div>\n          )}\n\n          <div className=\"space-y-6\">\n            <FileUploadSection\n              documentType=\"ID\"\n              title=\"Government-issued ID\"\n              description=\"Upload a clear photo of your passport, driver's license, or national ID card\"\n            />\n\n            <FileUploadSection\n              documentType=\"SELFIE\"\n              title=\"Selfie with ID\"\n              description=\"Take a selfie holding your ID document next to your face\"\n            />\n          </div>\n\n          <div className=\"mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg\">\n            <h4 className=\"text-sm font-medium text-yellow-900 mb-2\">Document Requirements:</h4>\n            <ul className=\"text-sm text-yellow-700 space-y-1\">\n              <li>• Documents must be clear and readable</li>\n              <li>• All four corners of the ID must be visible</li>\n              <li>• No blurred, cropped, or edited images</li>\n              <li>• Selfie must clearly show your face and the ID</li>\n              <li>• File formats: JPG, PNG (max 5MB each)</li>\n            </ul>\n          </div>\n        </CardContent>\n      </Card>\n    </div>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;;;AALA;;;;;AAiBO,MAAM,YAAsB;;IACjC,MAAM,EAAE,IAAI,EAAE,WAAW,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IACpC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB,EAAE;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;+BAAE;YACR;QACF;8BAAG,EAAE;IAEL,MAAM,oBAAoB;QACxB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;gBACjD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,aAAa,KAAK,IAAI;gBACxB;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,mBAAmB,OAAO,cAA+B;QAC7D,aAAa;QACb,eAAe;QAEf,IAAI;YACF,gBAAgB;YAChB,IAAI,CAAC,KAAK,IAAI,CAAC,UAAU,CAAC,WAAW;gBACnC,MAAM,IAAI,MAAM;YAClB;YAEA,IAAI,KAAK,IAAI,GAAG,IAAI,OAAO,MAAM;gBAC/B,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,WAAW,IAAI;YACrB,SAAS,MAAM,CAAC,QAAQ;YACxB,SAAS,MAAM,CAAC,gBAAgB;YAEhC,MAAM,WAAW,MAAM,MAAM,mBAAmB;gBAC9C,QAAQ;gBACR,aAAa;gBACb,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,CAAC,KAAK,OAAO,EAAE;gBACjB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;YAChC;YAEA,kCAAkC;YAClC,MAAM;YACN,MAAM;QAER,EAAE,OAAO,KAAU;YACjB,eAAe,IAAI,OAAO,IAAI;QAChC,SAAU;YACR,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB,CAAC;QACrB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,8NAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;YAChC,KAAK;gBACH,qBAAO,6LAAC,+MAAA,CAAA,UAAO;oBAAC,WAAU;;;;;;YAC5B,KAAK;gBACH,qBAAO,6LAAC,uMAAA,CAAA,QAAK;oBAAC,WAAU;;;;;;YAC1B;gBACE,qBAAO,6LAAC,uNAAA,CAAA,cAAW;oBAAC,WAAU;;;;;;QAClC;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAO,UAAU,IAAI,CAAC,CAAA,MAAO,IAAI,YAAY,KAAK;IACpD;IAEA,MAAM,oBAID,CAAC,EAAE,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE;QACxC,MAAM,cAAc,kBAAkB;QACtC,MAAM,UAAU,CAAC,KAAK,EAAE,cAAc;QAEtC,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;;8CACC,6LAAC;oCAAG,WAAU;8CAAuC;;;;;;8CACrD,6LAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;wBAEvC,6BACC,6LAAC;4BAAI,WAAU;;gCACZ,cAAc,YAAY,MAAM;8CACjC,6LAAC;oCAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,YAAY,MAAM,GAAG;8CAChG,YAAY,MAAM;;;;;;;;;;;;;;;;;;gBAM1B,4BACC,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,KAAK,YAAY,QAAQ;oCACzB,KAAK,GAAG,aAAa,SAAS,CAAC;oCAC/B,WAAU;;;;;;8CAEZ,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAE,WAAU;sDACV,iBAAiB,OAAO,gBAAgB;;;;;;sDAE3C,6LAAC;4CAAE,WAAU;;gDAAwB;gDACtB,IAAI,KAAK,YAAY,SAAS,EAAE,kBAAkB;;;;;;;wCAEhE,YAAY,eAAe,kBAC1B,6LAAC;4CAAE,WAAU;;gDAA4B;gDACpB,YAAY,eAAe;;;;;;;;;;;;;;;;;;;wBAMrD,YAAY,MAAM,KAAK,4BACtB,6LAAC;;8CACC,6LAAC;oCACC,IAAI;oCACJ,MAAK;oCACL,QAAO;oCACP,UAAU,CAAC;wCACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;wCAChC,IAAI,MAAM;4CACR,iBAAiB,cAAc;wCACjC;oCACF;oCACA,WAAU;;;;;;8CAEZ,6LAAC;oCACC,SAAS;oCACT,WAAU;;sDAEV,6LAAC,yMAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;;;;;;;;;;yCAO3C,6LAAC;;sCACC,6LAAC;4BACC,IAAI;4BACJ,MAAK;4BACL,QAAO;4BACP,UAAU,CAAC;gCACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gCAChC,IAAI,MAAM;oCACR,iBAAiB,cAAc;gCACjC;4BACF;4BACA,WAAU;;;;;;sCAEZ,6LAAC;4BACC,SAAS;4BACT,WAAU;sCAEV,cAAA,6LAAC;gCAAI,WAAU;;kDACb,6LAAC,yMAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6LAAC;wCAAE,WAAU;;0DACX,6LAAC;gDAAK,WAAU;0DAAgB;;;;;;4CAAsB;;;;;;;kDAExD,6LAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAOnD;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,yMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;;;;;0CACf,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;IAKzB;IAEA,qBACE,6LAAC;QAAI,WAAU;;0BAEb,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;4BAAC,WAAU;;8CACnB,6LAAC,yMAAA,CAAA,SAAM;oCAAC,WAAU;;;;;;8CAClB,6LAAC;8CAAK;;;;;;;;;;;;;;;;;kCAGV,6LAAC,mIAAA,CAAA,cAAW;;0CACV,6LAAC;gCAAI,WAAU;;oCACZ,cAAc,MAAM,aAAa;kDAClC,6LAAC;;0DACC,6LAAC;gDAAE,WAAU;;oDAAsC;kEACzC,6LAAC;wDAAK,WAAW,GACvB,MAAM,cAAc,aAAa,iBACjC,MAAM,cAAc,aAAa,iBACjC,kBACA;kEACC,MAAM,aAAa;;;;;;;;;;;;0DAGxB,6LAAC;gDAAE,WAAU;;oDACV,MAAM,cAAc,cAAc;oDAClC,MAAM,cAAc,aAAa;oDACjC,MAAM,cAAc,cAAc;;;;;;;;;;;;;;;;;;;4BAKxC,MAAM,cAAc,4BACnB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAyC;;;;;;kDACvD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQd,6LAAC,mIAAA,CAAA,OAAI;;kCACH,6LAAC,mIAAA,CAAA,aAAU;kCACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6LAAC,mIAAA,CAAA,cAAW;;4BACT,6BACC,6LAAC;gCAAI,WAAU;0CACZ;;;;;;4BAIJ,2BACC,6LAAC;gCAAI,WAAU;0CAAoF;;;;;;0CAKrG,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCACC,cAAa;wCACb,OAAM;wCACN,aAAY;;;;;;kDAGd,6LAAC;wCACC,cAAa;wCACb,OAAM;wCACN,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2C;;;;;;kDACzD,6LAAC;wCAAG,WAAU;;0DACZ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;0DACJ,6LAAC;0DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOlB;GA3Ta;;QACmB,2HAAA,CAAA,UAAO;;;KAD1B", "debugId": null}}, {"offset": {"line": 8090, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/components/dashboard/MiningUnitsTable.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { <PERSON>, CardHeader, CardTitle, CardContent } from '@/components/ui';\nimport { MiningRig } from '@/components/icons';\nimport { Calendar, TrendingUp, DollarSign } from 'lucide-react';\nimport { formatCurrency, formatTHS, formatDate, formatNumber } from '@/lib/utils';\n\ninterface MiningUnit {\n  id: string;\n  thsAmount: number;\n  investmentAmount: number;\n  startDate: string;\n  expiryDate: string;\n  dailyROI: number;\n  totalEarned: number;\n  status: 'ACTIVE' | 'EXPIRED';\n  createdAt: string;\n}\n\nexport const MiningUnitsTable: React.FC = () => {\n  const [miningUnits, setMiningUnits] = useState<MiningUnit[]>([]);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    fetchMiningUnits();\n  }, []);\n\n  const fetchMiningUnits = async () => {\n    try {\n      const response = await fetch('/api/mining-units', {\n        credentials: 'include',\n      });\n\n      if (response.ok) {\n        const data = await response.json();\n        if (data.success) {\n          setMiningUnits(data.data);\n        }\n      }\n    } catch (error) {\n      console.error('Failed to fetch mining units:', error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const calculateProgress = (unit: MiningUnit) => {\n    const maxEarnings = unit.investmentAmount * 5;\n    const progress = (unit.totalEarned / maxEarnings) * 100;\n    return Math.min(progress, 100);\n  };\n\n  const getDaysRemaining = (expiryDate: string) => {\n    const expiry = new Date(expiryDate);\n    const now = new Date();\n    const diffTime = expiry.getTime() - now.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return Math.max(diffDays, 0);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'ACTIVE':\n        return 'bg-eco-100 text-eco-700';\n      case 'EXPIRED':\n        return 'bg-gray-100 text-gray-700';\n      default:\n        return 'bg-gray-100 text-gray-700';\n    }\n  };\n\n  if (loading) {\n    return (\n      <Card>\n        <CardHeader>\n          <CardTitle className=\"flex items-center space-x-2\">\n            <MiningRig className=\"h-5 w-5 text-solar-500\" />\n            <span>Mining Units</span>\n          </CardTitle>\n        </CardHeader>\n        <CardContent>\n          <div className=\"animate-pulse space-y-4\">\n            {Array.from({ length: 3 }).map((_, i) => (\n              <div key={i} className=\"h-16 bg-gray-200 rounded-lg\"></div>\n            ))}\n          </div>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardTitle className=\"flex items-center space-x-2\">\n          <MiningRig className=\"h-5 w-5 text-solar-500\" />\n          <span>Mining Units ({miningUnits.length})</span>\n        </CardTitle>\n      </CardHeader>\n      <CardContent>\n        {miningUnits.length > 0 ? (\n          <div className=\"space-y-4\">\n            {/* Desktop Table */}\n            <div className=\"hidden md:block overflow-x-auto\">\n              <table className=\"w-full\">\n                <thead>\n                  <tr className=\"border-b border-gray-200\">\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Mining Power</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Investment</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Daily ROI</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Total Earned</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Progress</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Expires</th>\n                    <th className=\"text-left py-3 px-4 font-medium text-gray-700\">Status</th>\n                  </tr>\n                </thead>\n                <tbody>\n                  {miningUnits.map((unit) => (\n                    <tr key={unit.id} className=\"border-b border-gray-100 hover:bg-gray-50\">\n                      <td className=\"py-4 px-4\">\n                        <div className=\"flex items-center space-x-2\">\n                          <MiningRig className=\"h-4 w-4 text-solar-500\" />\n                          <span className=\"font-medium\">{formatTHS(unit.thsAmount)}</span>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <span className=\"font-medium\">{formatCurrency(unit.investmentAmount)}</span>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <span className=\"text-eco-600 font-medium\">\n                          {formatNumber(unit.dailyROI, 2)}%\n                        </span>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <div>\n                          <span className=\"font-medium text-eco-600\">\n                            {formatCurrency(unit.totalEarned)}\n                          </span>\n                          <div className=\"text-xs text-gray-500\">\n                            / {formatCurrency(unit.investmentAmount * 5)} max\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                          <div\n                            className=\"bg-eco-500 h-2 rounded-full transition-all duration-300\"\n                            style={{ width: `${calculateProgress(unit)}%` }}\n                          ></div>\n                        </div>\n                        <div className=\"text-xs text-gray-500 mt-1\">\n                          {formatNumber(calculateProgress(unit), 1)}%\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <div>\n                          <span className=\"text-sm\">{formatDate(unit.expiryDate)}</span>\n                          <div className=\"text-xs text-gray-500\">\n                            {getDaysRemaining(unit.expiryDate)} days left\n                          </div>\n                        </div>\n                      </td>\n                      <td className=\"py-4 px-4\">\n                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>\n                          {unit.status}\n                        </span>\n                      </td>\n                    </tr>\n                  ))}\n                </tbody>\n              </table>\n            </div>\n\n            {/* Mobile Cards */}\n            <div className=\"md:hidden space-y-4\">\n              {miningUnits.map((unit) => (\n                <div key={unit.id} className=\"bg-gray-50 rounded-lg p-4\">\n                  <div className=\"flex items-center justify-between mb-3\">\n                    <div className=\"flex items-center space-x-2\">\n                      <MiningRig className=\"h-5 w-5 text-solar-500\" />\n                      <span className=\"font-semibold\">{formatTHS(unit.thsAmount)}</span>\n                    </div>\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(unit.status)}`}>\n                      {unit.status}\n                    </span>\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4 mb-3\">\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Investment</div>\n                      <div className=\"font-medium\">{formatCurrency(unit.investmentAmount)}</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Daily ROI</div>\n                      <div className=\"font-medium text-eco-600\">{formatNumber(unit.dailyROI, 2)}%</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Total Earned</div>\n                      <div className=\"font-medium text-eco-600\">{formatCurrency(unit.totalEarned)}</div>\n                    </div>\n                    <div>\n                      <div className=\"text-xs text-gray-500\">Days Left</div>\n                      <div className=\"font-medium\">{getDaysRemaining(unit.expiryDate)}</div>\n                    </div>\n                  </div>\n\n                  <div className=\"mb-2\">\n                    <div className=\"flex justify-between text-xs text-gray-500 mb-1\">\n                      <span>Progress to 5x</span>\n                      <span>{formatNumber(calculateProgress(unit), 1)}%</span>\n                    </div>\n                    <div className=\"w-full bg-gray-200 rounded-full h-2\">\n                      <div\n                        className=\"bg-eco-500 h-2 rounded-full transition-all duration-300\"\n                        style={{ width: `${calculateProgress(unit)}%` }}\n                      ></div>\n                    </div>\n                  </div>\n\n                  <div className=\"text-xs text-gray-500\">\n                    Started: {formatDate(unit.startDate)} • Expires: {formatDate(unit.expiryDate)}\n                  </div>\n                </div>\n              ))}\n            </div>\n\n            {/* Summary */}\n            <div className=\"mt-6 p-4 bg-solar-50 rounded-lg\">\n              <h4 className=\"font-medium text-dark-900 mb-2\">Summary</h4>\n              <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 text-sm\">\n                <div>\n                  <span className=\"text-gray-600\">Total Mining Power: </span>\n                  <span className=\"font-medium\">\n                    {formatTHS(miningUnits.reduce((sum, unit) => sum + unit.thsAmount, 0))}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Total Investment: </span>\n                  <span className=\"font-medium\">\n                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.investmentAmount, 0))}\n                  </span>\n                </div>\n                <div>\n                  <span className=\"text-gray-600\">Total Earned: </span>\n                  <span className=\"font-medium text-eco-600\">\n                    {formatCurrency(miningUnits.reduce((sum, unit) => sum + unit.totalEarned, 0))}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        ) : (\n          <div className=\"text-center py-12\">\n            <MiningRig className=\"h-16 w-16 text-gray-300 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Mining Units</h3>\n            <p className=\"text-gray-500 mb-4\">\n              You haven't purchased any mining units yet. Start mining to earn daily returns!\n            </p>\n          </div>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AAAA;AAEA;;;AANA;;;;;AAoBO,MAAM,mBAA6B;;IACxC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAgB,EAAE;IAC/D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;sCAAE;YACR;QACF;qCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,qBAAqB;gBAChD,aAAa;YACf;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,IAAI,KAAK,OAAO,EAAE;oBAChB,eAAe,KAAK,IAAI;gBAC1B;YACF;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iCAAiC;QACjD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,MAAM,cAAc,KAAK,gBAAgB,GAAG;QAC5C,MAAM,WAAW,AAAC,KAAK,WAAW,GAAG,cAAe;QACpD,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,SAAS,IAAI,KAAK;QACxB,MAAM,MAAM,IAAI;QAChB,MAAM,WAAW,OAAO,OAAO,KAAK,IAAI,OAAO;QAC/C,MAAM,WAAW,KAAK,IAAI,CAAC,WAAW,CAAC,OAAO,KAAK,KAAK,EAAE;QAC1D,OAAO,KAAK,GAAG,CAAC,UAAU;IAC5B;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC,mIAAA,CAAA,OAAI;;8BACH,6LAAC,mIAAA,CAAA,aAAU;8BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;wBAAC,WAAU;;0CACnB,6LAAC,2IAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;0CACrB,6LAAC;0CAAK;;;;;;;;;;;;;;;;;8BAGV,6LAAC,mIAAA,CAAA,cAAW;8BACV,cAAA,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,kBACjC,6LAAC;gCAAY,WAAU;+BAAb;;;;;;;;;;;;;;;;;;;;;IAMtB;IAEA,qBACE,6LAAC,mIAAA,CAAA,OAAI;;0BACH,6LAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,6LAAC,mIAAA,CAAA,YAAS;oBAAC,WAAU;;sCACnB,6LAAC,2IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;;gCAAK;gCAAe,YAAY,MAAM;gCAAC;;;;;;;;;;;;;;;;;;0BAG5C,6LAAC,mIAAA,CAAA,cAAW;0BACT,YAAY,MAAM,GAAG,kBACpB,6LAAC;oBAAI,WAAU;;sCAEb,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAM,WAAU;;kDACf,6LAAC;kDACC,cAAA,6LAAC;4CAAG,WAAU;;8DACZ,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;8DAC9D,6LAAC;oDAAG,WAAU;8DAAgD;;;;;;;;;;;;;;;;;kDAGlE,6LAAC;kDACE,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;gDAAiB,WAAU;;kEAC1B,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAI,WAAU;;8EACb,6LAAC,2IAAA,CAAA,YAAS;oEAAC,WAAU;;;;;;8EACrB,6LAAC;oEAAK,WAAU;8EAAe,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;;;;;;kEAG3D,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;sEAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;kEAErE,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAU;;gEACb,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,EAAE;gEAAG;;;;;;;;;;;;kEAGpC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW;;;;;;8EAElC,6LAAC;oEAAI,WAAU;;wEAAwB;wEAClC,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB,GAAG;wEAAG;;;;;;;;;;;;;;;;;;kEAInD,6LAAC;wDAAG,WAAU;;0EACZ,6LAAC;gEAAI,WAAU;0EACb,cAAA,6LAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,OAAO,GAAG,kBAAkB,MAAM,CAAC,CAAC;oEAAC;;;;;;;;;;;0EAGlD,6LAAC;gEAAI,WAAU;;oEACZ,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,OAAO;oEAAG;;;;;;;;;;;;;kEAG9C,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;;8EACC,6LAAC;oEAAK,WAAU;8EAAW,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;8EACrD,6LAAC;oEAAI,WAAU;;wEACZ,iBAAiB,KAAK,UAAU;wEAAE;;;;;;;;;;;;;;;;;;kEAIzC,6LAAC;wDAAG,WAAU;kEACZ,cAAA,6LAAC;4DAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;sEACzF,KAAK,MAAM;;;;;;;;;;;;+CA9CT,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;sCAwDxB,6LAAC;4BAAI,WAAU;sCACZ,YAAY,GAAG,CAAC,CAAC,qBAChB,6LAAC;oCAAkB,WAAU;;sDAC3B,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC,2IAAA,CAAA,YAAS;4DAAC,WAAU;;;;;;sEACrB,6LAAC;4DAAK,WAAU;sEAAiB,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,KAAK,SAAS;;;;;;;;;;;;8DAE3D,6LAAC;oDAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;8DACzF,KAAK,MAAM;;;;;;;;;;;;sDAIhB,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEAAe,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,gBAAgB;;;;;;;;;;;;8DAEpE,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;;gEAA4B,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,KAAK,QAAQ,EAAE;gEAAG;;;;;;;;;;;;;8DAE5E,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEAA4B,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,KAAK,WAAW;;;;;;;;;;;;8DAE5E,6LAAC;;sEACC,6LAAC;4DAAI,WAAU;sEAAwB;;;;;;sEACvC,6LAAC;4DAAI,WAAU;sEAAe,iBAAiB,KAAK,UAAU;;;;;;;;;;;;;;;;;;sDAIlE,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;sEAAK;;;;;;sEACN,6LAAC;;gEAAM,CAAA,GAAA,sHAAA,CAAA,eAAY,AAAD,EAAE,kBAAkB,OAAO;gEAAG;;;;;;;;;;;;;8DAElD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDACC,WAAU;wDACV,OAAO;4DAAE,OAAO,GAAG,kBAAkB,MAAM,CAAC,CAAC;wDAAC;;;;;;;;;;;;;;;;;sDAKpD,6LAAC;4CAAI,WAAU;;gDAAwB;gDAC3B,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,SAAS;gDAAE;gDAAa,CAAA,GAAA,sHAAA,CAAA,aAAU,AAAD,EAAE,KAAK,UAAU;;;;;;;;mCA5CtE,KAAK,EAAE;;;;;;;;;;sCAmDrB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAAiC;;;;;;8CAC/C,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,YAAS,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,SAAS,EAAE;;;;;;;;;;;;sDAGvE,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,gBAAgB,EAAE;;;;;;;;;;;;sDAGnF,6LAAC;;8DACC,6LAAC;oDAAK,WAAU;8DAAgB;;;;;;8DAChC,6LAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,iBAAc,AAAD,EAAE,YAAY,MAAM,CAAC,CAAC,KAAK,OAAS,MAAM,KAAK,WAAW,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;yCAOpF,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,2IAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCACrB,6LAAC;4BAAG,WAAU;sCAAyC;;;;;;sCACvD,6LAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GApPa;KAAA", "debugId": null}}, {"offset": {"line": 8932, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/Hash_Minings/hashcorex/src/app/%28dashboard%29/dashboard/page.tsx"], "sourcesContent": ["'use client';\n\nimport React, { useState, useEffect } from 'react';\nimport { useAuth } from '@/hooks/useAuth';\nimport { useRouter } from 'next/navigation';\nimport { DashboardLayout } from '@/components/dashboard/DashboardLayout';\nimport { DashboardOverview } from '@/components/dashboard/DashboardOverview';\nimport { PurchaseMiningUnit } from '@/components/dashboard/PurchaseMiningUnit';\nimport { EarningsTracker } from '@/components/dashboard/EarningsTracker';\nimport { WalletDashboard } from '@/components/dashboard/WalletDashboard';\nimport { BinaryTreeVisualizer } from '@/components/dashboard/BinaryTreeVisualizer';\nimport { KYCPortal } from '@/components/dashboard/KYCPortal';\nimport { MiningUnitsTable } from '@/components/dashboard/MiningUnitsTable';\nimport { Loading } from '@/components/ui';\n\nexport default function DashboardPage() {\n  const { user, loading } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState('overview');\n\n  useEffect(() => {\n    if (!loading && !user) {\n      router.push('/login');\n    }\n  }, [user, loading, router]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <Loading size=\"lg\" text=\"Loading dashboard...\" />\n      </div>\n    );\n  }\n\n  if (!user) {\n    return null;\n  }\n\n  const renderTabContent = () => {\n    switch (activeTab) {\n      case 'overview':\n        return <DashboardOverview />;\n      case 'mining':\n        return (\n          <div className=\"space-y-6\">\n            <PurchaseMiningUnit onPurchaseComplete={() => window.location.reload()} />\n            <MiningUnitsTable />\n          </div>\n        );\n      case 'earnings':\n        return <EarningsTracker />;\n      case 'wallet':\n        return <WalletDashboard />;\n      case 'referrals':\n        return <BinaryTreeVisualizer />;\n      case 'kyc':\n        return <KYCPortal />;\n      case 'admin':\n        // Redirect to admin page for admin users\n        if (user?.role === 'ADMIN') {\n          router.push('/admin');\n          return <DashboardOverview />;\n        }\n        return <DashboardOverview />;\n      default:\n        return <DashboardOverview />;\n    }\n  };\n\n  return (\n    <DashboardLayout activeTab={activeTab} onTabChange={setActiveTab}>\n      {renderTabContent()}\n    </DashboardLayout>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;;;AAbA;;;;;;;;;;;;;AAee,SAAS;;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAChC,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR,IAAI,CAAC,WAAW,CAAC,MAAM;gBACrB,OAAO,IAAI,CAAC;YACd;QACF;kCAAG;QAAC;QAAM;QAAS;KAAO;IAE1B,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC,sIAAA,CAAA,UAAO;gBAAC,MAAK;gBAAK,MAAK;;;;;;;;;;;IAG9B;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,MAAM,mBAAmB;QACvB,OAAQ;YACN,KAAK;gBACH,qBAAO,6LAAC,uJAAA,CAAA,oBAAiB;;;;;YAC3B,KAAK;gBACH,qBACE,6LAAC;oBAAI,WAAU;;sCACb,6LAAC,wJAAA,CAAA,qBAAkB;4BAAC,oBAAoB,IAAM,OAAO,QAAQ,CAAC,MAAM;;;;;;sCACpE,6LAAC,sJAAA,CAAA,mBAAgB;;;;;;;;;;;YAGvB,KAAK;gBACH,qBAAO,6LAAC,qJAAA,CAAA,kBAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,qJAAA,CAAA,kBAAe;;;;;YACzB,KAAK;gBACH,qBAAO,6LAAC,0JAAA,CAAA,uBAAoB;;;;;YAC9B,KAAK;gBACH,qBAAO,6LAAC,+IAAA,CAAA,YAAS;;;;;YACnB,KAAK;gBACH,yCAAyC;gBACzC,IAAI,MAAM,SAAS,SAAS;oBAC1B,OAAO,IAAI,CAAC;oBACZ,qBAAO,6LAAC,uJAAA,CAAA,oBAAiB;;;;;gBAC3B;gBACA,qBAAO,6LAAC,uJAAA,CAAA,oBAAiB;;;;;YAC3B;gBACE,qBAAO,6LAAC,uJAAA,CAAA,oBAAiB;;;;;QAC7B;IACF;IAEA,qBACE,6LAAC,qJAAA,CAAA,kBAAe;QAAC,WAAW;QAAW,aAAa;kBACjD;;;;;;AAGP;GA3DwB;;QACI,2HAAA,CAAA,UAAO;QAClB,qIAAA,CAAA,YAAS;;;KAFF", "debugId": null}}]}