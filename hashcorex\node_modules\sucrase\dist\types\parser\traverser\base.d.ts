import State from "../tokenizer/state";
export declare let isJSXEnabled: boolean;
export declare let isTypeScriptEnabled: boolean;
export declare let isFlowEnabled: boolean;
export declare let state: State;
export declare let input: string;
export declare let nextContextId: number;
export declare function getNextContextId(): number;
export declare function augmentError(error: any): any;
export declare class Loc {
    line: number;
    column: number;
    constructor(line: number, column: number);
}
export declare function locationForIndex(pos: number): Loc;
export declare function initParser(inputCode: string, isJSXEnabledArg: boolean, isTypeScriptEnabledArg: boolean, isFlowEnabledArg: boolean): void;
