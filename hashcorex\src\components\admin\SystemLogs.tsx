'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardHeader, CardTitle, CardContent, Button, Input } from '@/components/ui';
import { 
  FileText, 
  Search, 
  Filter, 
  Download,
  Calendar,
  User,
  Activity,
  AlertTriangle,
  Info,
  CheckCircle,
  XCircle
} from 'lucide-react';
import { formatDate } from '@/lib/utils';

interface SystemLog {
  id: string;
  action: string;
  userId?: string;
  user?: {
    firstName: string;
    lastName: string;
    email: string;
  };
  details: any;
  ipAddress: string;
  userAgent: string;
  createdAt: string;
}

export const SystemLogs: React.FC = () => {
  const [logs, setLogs] = useState<SystemLog[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterAction, setFilterAction] = useState<string>('all');
  const [dateRange, setDateRange] = useState<'today' | 'week' | 'month' | 'all'>('today');
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);

  useEffect(() => {
    fetchLogs();
  }, [currentPage, searchTerm, filterAction, dateRange]);

  const fetchLogs = async () => {
    try {
      setLoading(true);
      const params = new URLSearchParams({
        page: currentPage.toString(),
        limit: '50',
        search: searchTerm,
        action: filterAction,
        dateRange,
      });

      const response = await fetch(`/api/admin/logs?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        if (data.success) {
          setLogs(data.data.logs);
          setTotalPages(data.data.totalPages);
        }
      }
    } catch (error) {
      console.error('Failed to fetch logs:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportLogs = async () => {
    try {
      const params = new URLSearchParams({
        search: searchTerm,
        action: filterAction,
        dateRange,
        export: 'true',
      });

      const response = await fetch(`/api/admin/logs/export?${params}`, {
        credentials: 'include',
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system-logs-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      }
    } catch (error) {
      console.error('Failed to export logs:', error);
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'USER_LOGIN':
      case 'USER_LOGOUT':
        return <User className="h-4 w-4 text-blue-500" />;
      case 'USER_REGISTER':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'MINING_PURCHASE':
      case 'WITHDRAWAL_REQUEST':
        return <Activity className="h-4 w-4 text-purple-500" />;
      case 'KYC_SUBMIT':
      case 'KYC_APPROVE':
      case 'KYC_REJECT':
        return <FileText className="h-4 w-4 text-orange-500" />;
      case 'ADMIN_ACTION':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getActionColor = (action: string) => {
    switch (action) {
      case 'USER_LOGIN':
      case 'USER_REGISTER':
      case 'KYC_APPROVE':
        return 'text-green-700 bg-green-100';
      case 'USER_LOGOUT':
        return 'text-blue-700 bg-blue-100';
      case 'MINING_PURCHASE':
      case 'WITHDRAWAL_REQUEST':
        return 'text-purple-700 bg-purple-100';
      case 'KYC_SUBMIT':
        return 'text-orange-700 bg-orange-100';
      case 'KYC_REJECT':
      case 'ADMIN_ACTION':
        return 'text-red-700 bg-red-100';
      default:
        return 'text-gray-700 bg-gray-100';
    }
  };

  const formatLogDetails = (details: any) => {
    if (typeof details === 'string') return details;
    if (typeof details === 'object') {
      return Object.entries(details)
        .map(([key, value]) => `${key}: ${value}`)
        .join(', ');
    }
    return JSON.stringify(details);
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-4"></div>
          <div className="h-64 bg-gray-200 rounded"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-gray-900">System Logs</h1>
          <p className="text-gray-600 mt-1">Monitor platform activity and user actions</p>
        </div>
        <Button
          onClick={exportLogs}
          variant="outline"
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Export Logs
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search logs..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div>
              <select
                value={filterAction}
                onChange={(e) => setFilterAction(e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
              >
                <option value="all">All Actions</option>
                <option value="USER_LOGIN">User Login</option>
                <option value="USER_REGISTER">User Register</option>
                <option value="MINING_PURCHASE">Mining Purchase</option>
                <option value="WITHDRAWAL_REQUEST">Withdrawal Request</option>
                <option value="KYC_SUBMIT">KYC Submit</option>
                <option value="ADMIN_ACTION">Admin Action</option>
              </select>
            </div>
            <div>
              <select
                value={dateRange}
                onChange={(e) => setDateRange(e.target.value as any)}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-solar-500 focus:border-transparent"
              >
                <option value="today">Today</option>
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="all">All Time</option>
              </select>
            </div>
            <div className="text-sm text-gray-600 flex items-center">
              <Activity className="h-4 w-4 mr-1" />
              {logs.length} logs found
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Logs Table */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Activity Logs
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            {logs.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No Logs Found</h3>
                <p className="text-gray-600">No activity logs match your current filters.</p>
              </div>
            ) : (
              logs.map((log) => (
                <div key={log.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50">
                  <div className="flex items-start justify-between">
                    <div className="flex items-start gap-3 flex-1">
                      <div className="mt-1">
                        {getActionIcon(log.action)}
                      </div>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>
                            {log.action.replace(/_/g, ' ')}
                          </span>
                          <span className="text-sm text-gray-500">
                            {formatDate(log.createdAt)}
                          </span>
                        </div>
                        
                        {log.user && (
                          <div className="text-sm text-gray-700 mb-1">
                            <span className="font-medium">
                              {log.user.firstName} {log.user.lastName}
                            </span>
                            <span className="text-gray-500 ml-2">({log.user.email})</span>
                          </div>
                        )}
                        
                        <div className="text-sm text-gray-600 mb-2">
                          {formatLogDetails(log.details)}
                        </div>
                        
                        <div className="flex items-center gap-4 text-xs text-gray-500">
                          <span>IP: {log.ipAddress}</span>
                          <span className="truncate max-w-xs">
                            User Agent: {log.userAgent}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-between mt-6 pt-6 border-t">
              <div className="text-sm text-gray-600">
                Page {currentPage} of {totalPages}
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                >
                  Next
                </Button>
              </div>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
};
