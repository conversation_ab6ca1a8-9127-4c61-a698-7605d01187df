// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            String   @id @default(cuid())
  email         String   @unique
  password      String
  referralId    String   @unique
  referrerId    String?
  role          UserRole @default(USER)
  isActive      Boolean  @default(true)
  kycStatus     KYCStatus @default(PENDING)
  leftReferralId  String?
  rightReferralId String?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  miningUnits     MiningUnit[]
  transactions    Transaction[]
  kycDocuments    KYCDocument[]
  withdrawalRequests WithdrawalRequest[]
  referrals       Referral[] @relation("ReferrerRelation")
  referredBy      Referral[] @relation("ReferredRelation")
  binaryPoints    BinaryPoints?
  systemLogs      SystemLog[]

  @@map("users")
}

model MiningUnit {
  id               String   @id @default(cuid())
  userId           String
  thsAmount        Float
  investmentAmount Float
  startDate        DateTime @default(now())
  expiryDate       DateTime
  dailyROI         Float
  totalEarned      Float    @default(0)
  status           MiningUnitStatus @default(ACTIVE)
  createdAt        DateTime @default(now())
  updatedAt        DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("mining_units")
}

model Transaction {
  id          String   @id @default(cuid())
  userId      String
  type        TransactionType
  amount      Float
  description String
  status      TransactionStatus @default(PENDING)
  reference   String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("transactions")
}

model Referral {
  id             String   @id @default(cuid())
  referrerId     String
  referredId     String
  placementSide  PlacementSide
  commissionEarned Float @default(0)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  referrer User @relation("ReferrerRelation", fields: [referrerId], references: [id], onDelete: Cascade)
  referred User @relation("ReferredRelation", fields: [referredId], references: [id], onDelete: Cascade)

  @@unique([referrerId, referredId])
  @@map("referrals")
}

model BinaryPoints {
  id            String   @id @default(cuid())
  userId        String   @unique
  leftPoints    Float    @default(0)
  rightPoints   Float    @default(0)
  matchedPoints Float    @default(0)
  flushDate     DateTime?
  createdAt     DateTime @default(now())
  updatedAt     DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("binary_points")
}

model KYCDocument {
  id             String   @id @default(cuid())
  userId         String
  documentType   DocumentType
  filePath       String
  status         KYCStatus @default(PENDING)
  reviewedAt     DateTime?
  reviewedBy     String?
  rejectionReason String?
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("kyc_documents")
}

model WithdrawalRequest {
  id              String   @id @default(cuid())
  userId          String
  amount          Float
  usdtAddress     String
  status          WithdrawalStatus @default(PENDING)
  txid            String?
  processedAt     DateTime?
  rejectionReason String?
  createdAt       DateTime @default(now())
  updatedAt       DateTime @updatedAt

  // Relations
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("withdrawal_requests")
}

model AdminSettings {
  id        String   @id @default(cuid())
  key       String   @unique
  value     String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("admin_settings")
}

model SystemLog {
  id        String   @id @default(cuid())
  action    String
  userId    String?
  adminId   String?
  details   String?
  ipAddress String?
  userAgent String?
  createdAt DateTime @default(now())

  // Relations
  user  User? @relation(fields: [userId], references: [id], onDelete: SetNull)

  @@map("system_logs")
}

// Enums
enum UserRole {
  USER
  ADMIN
}

enum KYCStatus {
  PENDING
  APPROVED
  REJECTED
}

enum MiningUnitStatus {
  ACTIVE
  EXPIRED
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  PURCHASE
  MINING_EARNINGS
  DIRECT_REFERRAL
  BINARY_BONUS
}

enum TransactionStatus {
  PENDING
  COMPLETED
  FAILED
}

enum PlacementSide {
  LEFT
  RIGHT
}

enum DocumentType {
  ID
  SELFIE
}

enum WithdrawalStatus {
  PENDING
  COMPLETED
  REJECTED
}
