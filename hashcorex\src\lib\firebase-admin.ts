import { initializeApp, getApps, cert } from 'firebase-admin/app';
import { getAuth } from 'firebase-admin/auth';

// Initialize Firebase Admin SDK
const firebaseAdminConfig = {
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  // For development, we'll use the Firebase project's default service account
  // In production, you should use a proper service account key
};

// Initialize Firebase Admin if not already initialized
const app = getApps().length === 0 
  ? initializeApp(firebaseAdminConfig)
  : getApps()[0];

export const adminAuth = getAuth(app);

// Firebase Admin Auth utilities
export const createFirebaseUser = async (userData: {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
}) => {
  try {
    const userRecord = await adminAuth.createUser({
      email: userData.email,
      password: userData.password,
      displayName: `${userData.firstName} ${userData.lastName}`,
      emailVerified: false,
    });

    return {
      uid: userRecord.uid,
      email: userRecord.email,
      displayName: userRecord.displayName,
    };
  } catch (error: any) {
    console.error('Error creating Firebase user:', error);
    throw new Error(`Failed to create Firebase user: ${error.message}`);
  }
};

export const updateFirebaseUser = async (uid: string, updates: {
  displayName?: string;
  email?: string;
  emailVerified?: boolean;
}) => {
  try {
    const userRecord = await adminAuth.updateUser(uid, updates);
    return userRecord;
  } catch (error: any) {
    console.error('Error updating Firebase user:', error);
    throw new Error(`Failed to update Firebase user: ${error.message}`);
  }
};

export const deleteFirebaseUser = async (uid: string) => {
  try {
    await adminAuth.deleteUser(uid);
    return true;
  } catch (error: any) {
    console.error('Error deleting Firebase user:', error);
    throw new Error(`Failed to delete Firebase user: ${error.message}`);
  }
};

export const getFirebaseUser = async (uid: string) => {
  try {
    const userRecord = await adminAuth.getUser(uid);
    return userRecord;
  } catch (error: any) {
    console.error('Error getting Firebase user:', error);
    return null;
  }
};

export const getFirebaseUserByEmail = async (email: string) => {
  try {
    const userRecord = await adminAuth.getUserByEmail(email);
    return userRecord;
  } catch (error: any) {
    console.error('Error getting Firebase user by email:', error);
    return null;
  }
};
